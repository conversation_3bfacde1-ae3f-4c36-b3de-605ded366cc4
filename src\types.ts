import { SupabaseProduct } from './index';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: string;
  category: string;
  categories?: string[]; // Opcional: si también manejas múltiples categorías
  image_url: string;
  industry?: string;
  brand: string;
  code: string;
  normative: string;
  stock: number;
  imageUrl: string;
  technicalSpecs: { [key: string]: string };
  características: string;
  especificaciones: string;
  presentación: string;
  documentacion: string;
  featured_status?: {
    is_featured: boolean;
    featured_type?: 'featured' | 'new' | 'top';
    end_date?: string;
  };
}

export interface CartItem {
  _id: string;
  name: string;
  price: string;
  quantity: number;
  brand: string;  // Añadiendo la marca
  category: string;
  image_url: string;
}

export interface ProductFilters {
  page: number;
  itemsPerPage: number;
  search?: string;
  categories?: string[];
  industries?: string[];
  brands?: string[];
  featured?: boolean;
  featuredType?: 'featured' | 'new' | 'top';
}

export interface FeaturedProduct {
  id: string;
  product_id: string;
  featured_type: 'featured' | 'new' | 'top';
  start_date: string;
  end_date: string | null;
  created_at: string;
  product_data: SupabaseProduct;
}

export interface FeaturedProductInput {
  product_id: string;
  featured_type: 'featured' | 'new' | 'top';
  start_date?: string;
  end_date?: string | null;
}

// Paginación genérica para respuestas de productos
export interface PaginatedResponse<T> {
  data: T[];
  count: number;
}
