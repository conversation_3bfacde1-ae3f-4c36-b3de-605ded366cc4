// Tipos existentes
export interface ExistingProduct {
  // ... mantener los tipos existentes
}

// Nuevos tipos para Supabase
export interface SupabaseProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image_url: string | null;
  industry: string | null;
  brand: string | null;
  stock: number;
  caracteristicas: string | null;
  especificaciones: string | null;
  presentacion: string | null;
  documentacion: string | null;
  created_at: string;
  updated_at: string;
}

// Tipo unificado que puede manejar ambos formatos
export type Product = ExistingProduct | SupabaseProduct;