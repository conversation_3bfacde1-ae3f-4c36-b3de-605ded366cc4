/* Estilos base para categorías y servicios */
.category-grid,
.services-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Modificar el fondo de la sección de categorías */
.categories-section {
  position: relative;
  width: 100%;
  background-color: #f3f4f6 !important; /* Un gris muy claro */
  padding: 4rem 0;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

/* Estilos base de tarjetas */
.category-card,
.service-card {
  @apply relative overflow-hidden rounded-xl
         bg-white border border-gray-100
         transition-all duration-300 ease-out
         hover:shadow-xl hover:-translate-y-1;
}

  /* Estilos específicos para categorías */
  .category-card {
    @apply p-6 cursor-pointer;
}

.category-icon-wrapper {
  @apply relative w-12 h-12 mb-4
         flex items-center justify-center
         rounded-lg bg-amber-100
         transition-all duration-300 ease-out
         group-hover:bg-amber-500;
}

.category-icon {
  @apply w-6 h-6 text-amber-600
         transition-all duration-300 ease-out
         group-hover:text-white
         group-hover:scale-110
         group-hover:rotate-[360deg];
}

.category-title {
  @apply text-xl font-semibold text-gray-900
         transition-colors duration-300
         group-hover:text-amber-600;
}

.category-description {
  @apply mt-2 text-gray-600
         transition-colors duration-300
         group-hover:text-gray-900;
}

/* Indicador de acción */
.category-card::after {
  @apply absolute bottom-0 left-0 w-full h-1
         bg-gradient-to-r from-amber-500 to-amber-600
         transform translate-y-full
         transition-transform duration-300 ease-out;
  content: '';
}

.category-card:hover::after {
  @apply translate-y-0;
}

  /* Estilos específicos para servicios */
  .service-card {
    @apply p-6 cursor-pointer;
}

/* Variantes de servicios */
.service-card--primary {
  @apply bg-gradient-to-br from-amber-50 to-amber-100
         border-amber-200;
}

.service-card--secondary {
  @apply bg-gradient-to-br from-gray-50 to-gray-100
         border-gray-200;
}

.service-card--featured {
  @apply bg-gradient-to-br from-amber-500 to-amber-600
         border-amber-600;
}

.service-icon-wrapper {
  @apply w-14 h-14 mb-4
         rounded-2xl
         flex items-center justify-center
         transition-all duration-300 ease-out;
}

.service-card--primary .service-icon-wrapper {
  @apply bg-amber-500 text-white;
}

.service-card--secondary .service-icon-wrapper {
  @apply bg-gray-700 text-white;
}

.service-card--featured .service-icon-wrapper {
  @apply bg-white text-amber-600;
}

.service-icon {
  @apply w-7 h-7
         transition-all duration-300 ease-out;
}

.service-card:hover .service-icon {
  @apply scale-110 rotate-12;
}

.service-title {
  @apply text-xl font-semibold mb-2
         transition-colors duration-300;
}

.service-card--primary .service-title {
  @apply text-gray-900;
}

.service-card--secondary .service-title {
  @apply text-gray-800;
}

.service-card--featured .service-title {
  @apply text-white;
}

.service-description {
  @apply transition-colors duration-300;
}

.service-card--primary .service-description,
.service-card--secondary .service-description {
  @apply text-gray-600;
}

.service-card--featured .service-description {
  @apply text-white/90;
}

/* Micro-interacciones */
@keyframes pulse-soft {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.category-card:hover .category-icon,
.service-card:hover .service-icon {
  animation: pulse-soft 2s infinite;
}

/* Indicadores de acción */
.service-card::before {
  @apply absolute top-4 right-4
         w-8 h-8
         rounded-full
         flex items-center justify-center
         opacity-0
         transition-all duration-300 ease-out;
  content: '→';
}

.service-card:hover::before {
  @apply opacity-100 translate-x-2;
}

/* Responsive */
@media (max-width: 640px) {
  .category-grid,
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}
