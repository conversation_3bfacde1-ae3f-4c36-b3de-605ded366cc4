import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { HardHat } from 'lucide-react';
import { IMAGES } from '../config/constants';

interface ImageToPreload {
  url: string;
  priority: number;
}

const LoadingScreen = ({ onLoadingComplete }: { onLoadingComplete: () => void }) => {
  const [progress, setProgress] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState(0);
  const [isPreloadComplete, setIsPreloadComplete] = useState(false);
  const [isFontLoaded, setIsFontLoaded] = useState(false);

  useEffect(() => {
    // Verificar si la fuente está cargada
    document.fonts.ready.then(() => {
      setIsFontLoaded(true);
    });

    const imagesToPreload: ImageToPreload[] = [
      { url: IMAGES.HERO_BACKGROUND, priority: 1 },
      { url: IMAGES.LOGO, priority: 1 },
      { url: IMAGES.PROMO_3M, priority: 2 },
      { url: IMAGES.PROMO_RINGO, priority: 2 },
      { url: IMAGES.PROMO_EPP, priority: 2 },
      { url: IMAGES.DEFAULT_PRODUCT, priority: 3 },
      { url: IMAGES.LOADING_HELMET, priority: 1 }
    ];

    const totalImages = imagesToPreload.length;
    let loadedCount = 0;

    const sortedImages = [...imagesToPreload].sort((a, b) => a.priority - b.priority);

    const preloadImage = (imageUrl: string): Promise<void> => {
      return new Promise((resolve) => {
        const img = new Image();
        
        const handleLoad = () => {
          loadedCount++;
          setImagesLoaded(loadedCount);
          resolve();
        };

        img.onload = handleLoad;
        img.onerror = () => {
          console.warn(`Failed to load image: ${imageUrl}`);
          handleLoad();
        };

        img.src = imageUrl;
      });
    };

    const preloadAllImages = async () => {
      try {
        await Promise.all(sortedImages.map(image => preloadImage(image.url)));
        setIsPreloadComplete(true);
      } catch (error) {
        console.error('Error preloading images:', error);
        setIsPreloadComplete(true);
      }
    };

    preloadAllImages();

    const timer = setInterval(() => {
      setProgress(prev => {
        const targetProgress = (isPreloadComplete && isFontLoaded) ? 100 : 
          ((imagesLoaded / totalImages) * 90) + (isFontLoaded ? 10 : 0);
        
        if (prev >= targetProgress) {
          return prev;
        }

        const newProgress = Math.min(prev + 4, targetProgress);
        
        if (newProgress >= 100 && isPreloadComplete && isFontLoaded) {
          clearInterval(timer);
          setTimeout(() => {
            onLoadingComplete();
          }, 100);
          return 100;
        }
        
        return newProgress;
      });
    }, 25);

    return () => clearInterval(timer);
  }, [onLoadingComplete, imagesLoaded, isPreloadComplete, isFontLoaded]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="fixed inset-0 bg-gray-900 flex flex-col items-center justify-center z-50"
    >
      <div className="w-full max-w-md px-4 flex flex-col items-center">
        <HardHat className="w-32 h-32 mb-8 text-amber-500 animate-bounce" />
        
        <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-4">
          <motion.div
            className="h-full bg-amber-500"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>

        <div className="flex items-center space-x-3 text-amber-500">
          <span className="text-lg font-medium">
            Cargando {progress.toFixed(0)}%
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default LoadingScreen;

