-- Crear función para sincronizar URLs de imágenes
create or replace function maintenance.sync_product_image_urls()
returns void as $$
declare
    product record;
    files record;
    public_url text;
begin
    for product in select id, image_url from public.products
    loop
        -- Buscar archivos en el bucket para este producto
        select array_agg(name) as names into files
        from storage.objects
        where bucket_id = 'product-images'
        and path like product.id || '/main.%';

        -- Si encontramos archivos que coincidan con el patrón main.*
        if files.names is not null and array_length(files.names, 1) > 0 then
            -- Obtener la URL pública del primer archivo encontrado
            select storage.fspath(
                'product-images',
                product.id || '/' || files.names[1]
            ) into public_url;

            -- Actualizar la URL en la tabla de productos
            update public.products
            set 
                image_url = public_url,
                updated_at = now()
            where id = product.id
            and (image_url is null or image_url != public_url);
        end if;
    end loop;
end;
$$ language plpgsql security definer;

-- Programar la sincronización para ejecutarse cada hora
select cron.schedule(
    'sync-product-image-urls',
    '0 3 * * *',  -- A las 3 AM todos los días
    $$select maintenance.sync_product_image_urls()$$
);