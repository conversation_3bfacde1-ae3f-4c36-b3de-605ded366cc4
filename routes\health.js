import express from 'express';
import { createClient } from '@supabase/supabase-js';
import logger from '../lib/logger.js';

const router = express.Router();
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Health check básico
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// Health check detallado
router.get('/health/detailed', async (req, res) => {
  try {
    // Verificar conexión con Supabase
    const { data: supabaseHealth, error: supabaseError } = await supabase
      .from('health_check')
      .select('count')
      .single();

    // Verificar uso de memoria
    const memoryUsage = process.memoryUsage();

    const healthStatus = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version,
      uptime: process.uptime(),
      memory: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
      },
      services: {
        supabase: supabaseError ? 'error' : 'ok',
      }
    };

    logger.info('Health check performed', healthStatus);
    res.json(healthStatus);
  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
});

export default router;