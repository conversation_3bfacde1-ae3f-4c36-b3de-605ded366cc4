import type { CatalogProduct } from '../types/catalog';

/**
 * Interfaz para representar la estructura de un producto crudo directamente desde Supabase
 * o después de una adaptación inicial de products.json.
 * La función transformRawProductToCatalogProduct espera este formato.
 */
export interface RawProductDataForSupabase { // Exportar y renombrar para claridad
  // Campos comunes entre products.json y Supabase
  id: string;
  name: string;
  description: string | null;
  price: number;
  image_url?: string | null; // Changed from imageUrl to match typical Supabase column names, can be null
  
  // Campos específicos de Supabase
  categories?: string[]; // Este es text[] en Supabase, contendrá los NUEVOS IDs de categoría
  brand?: string | null;
  stock?: number | null;
  icon?: string | null;
  industries?: string[] | null;
  caracteristicas?: string | null; // Campo directo en Supabase
  especificaciones?: string | null; // Campo directo en Supabase
  presentacion?: string | null; // Campo directo en Supabase
  documentation?: string | null; // Renombrado a documentationUrl en CatalogProduct

  // Campo específico de products.json (para migración)
  originalCategory_from_json?: string; // Para diferenciar de 'categories' de Supabase
  technicalSpecs_from_json?: { // Para diferenciar de campos directos de Supabase
    Características?: string;
    Especificaciones?: string;
    Presentación?: string;
  };
   // Permite otras propiedades no definidas explícitamente
  [key: string]: any;
}

/**
 * Verifica si una cadena es una URL válida.
 * @param str La cadena a verificar.
 * @returns True si es una URL válida, false en caso contrario.
 */
function isValidHttpUrl(str: string): boolean {
  let url;
  try {
    url = new URL(str);
  } catch (_) {
    return false;
  }
  return url.protocol === "http:" || url.protocol === "https:";
}

/**
 * Transforma un objeto de producto crudo al formato CatalogProduct.
 * @param rawProduct El objeto de producto crudo.
 * @returns Un objeto CatalogProduct transformado.
 */
export function transformRawProductToCatalogProduct(rawProduct: RawProductDataForSupabase): CatalogProduct {
  let currentDescription = rawProduct.description;
  let currentImageUrl = rawProduct.image_url; // Changed from rawProduct.imageUrl

  // Si no hay imageUrl explícito y la descripción (de products.json) parece una URL, usarla como imageUrl
  // Para datos de Supabase, image_url ya debería estar poblado correctamente.
  if (!currentImageUrl && rawProduct.originalCategory_from_json && rawProduct.description && isValidHttpUrl(rawProduct.description)) {
    currentImageUrl = rawProduct.description;
    // Opcionalmente, limpiar la descripción si era una URL
    // currentDescription = '';
  }
  
  // Determinar technicalSpecs y asegurar que no sea undefined
  // También asegurar que sus subcampos tengan valores por defecto si se usan para poblar campos directos
  let determinedTechnicalSpecs: { caracteristicas?: string; especificaciones?: string; presentacion?: string; } = {};

  if (rawProduct.technicalSpecs_from_json) {
    determinedTechnicalSpecs = {
      caracteristicas: rawProduct.technicalSpecs_from_json.Características || '',
      especificaciones: rawProduct.technicalSpecs_from_json.Especificaciones || '',
      presentacion: rawProduct.technicalSpecs_from_json.Presentación || '',
    };
  } else if (rawProduct.caracteristicas || rawProduct.especificaciones || rawProduct.presentacion) {
    determinedTechnicalSpecs = {
      caracteristicas: rawProduct.caracteristicas || '',
      especificaciones: rawProduct.especificaciones || '',
      presentacion: rawProduct.presentacion || '',
    };
  }

  const catalogProduct: CatalogProduct = {
    id: rawProduct.id,
    name: rawProduct.name || '',
    description: currentDescription || '',
    price: String(rawProduct.price || 0),
    image_url: currentImageUrl || '',
    imageUrl: currentImageUrl || '',
    brand: rawProduct.brand || '',
    stock: rawProduct.stock || 0,
    icon: rawProduct.icon || '', // Asumiendo que icon también podría necesitar un default si se hace requerido
    industries: rawProduct.industries || [],
    originalCategory_from_json: rawProduct.originalCategory_from_json,
    categoryIds: rawProduct.categories || [],
    technicalSpecs: determinedTechnicalSpecs, // Ahora siempre es un objeto
    documentacion: rawProduct.documentation || '',
    category: '', // Will be set by assignNewCategoryIds
    code: rawProduct.code || '',
    normative: rawProduct.normative || '',
    featured_status: rawProduct.featured_status, // Este es opcional, así que está bien si es undefined
    
    // Poblar los campos directos requeridos desde determinedTechnicalSpecs
    características: determinedTechnicalSpecs.caracteristicas || '',
    especificaciones: determinedTechnicalSpecs.especificaciones || '',
    presentación: determinedTechnicalSpecs.presentacion || '',
  };
  
  // La lógica de poblar características, etc., desde technicalSpecs ya está integrada arriba.

  // Siempre llamar a assignNewCategoryIds para procesar y mapear categoryIds
  // y establecer el campo 'category' correctamente.
  return assignNewCategoryIds(catalogProduct);
}

/**
 * Ejemplo de cómo podrías mapear categorías antiguas a las nuevas IDs de categorías.
 * Esto es solo un ejemplo y necesitará ser ajustado a tus datos y lógica específicos.
 */
const categoryMapping: Record<string, string[]> = {
  "Seguridad Industrial": ["proteccion-manos"],
  "Calzado": ["corporal-calzado"],
  "Protección": ["proteccion-craneana"], // Key for old "Cascos" category
  "Indumentaria": ["corporal-indumentaria"],
  "Seguridad": ["accesorios"],
  // Si tienes más categorías originales en el futuro, añádelas aquí.
};

/**
 * Asigna las nuevas IDs de categoría a un producto.
 * Procesa `originalCategory_from_json` y también los `categoryIds` existentes
 * (que podrían ser nombres antiguos de `rawProduct.categories`).
 * Actualiza `product.categoryIds` y `product.category`.
 * @param product El producto a actualizar.
 * @returns El producto con `categoryIds` y `category` actualizados.
 */
export function assignNewCategoryIds(product: CatalogProduct): CatalogProduct {
  const currentIdsFromProduct = product.categoryIds || [];
  const newIdsSet = new Set<string>();

  // 1. Intentar mapear desde originalCategory_from_json si está presente
  if (product.originalCategory_from_json) {
    const mappedFromOriginal = categoryMapping[product.originalCategory_from_json];
    if (mappedFromOriginal) {
      mappedFromOriginal.forEach(id => newIdsSet.add(id));
    } else {
      console.warn(`No category mapping found for originalCategory_from_json: "${product.originalCategory_from_json}" for product "${product.name}"`);
      // Si no hay mapeo para originalCategory_from_json, pero es un string, podría ser un ID válido o un nombre antiguo.
      // Lo procesaremos en el siguiente paso si también está en currentIdsFromProduct o si currentIdsFromProduct está vacío.
    }
  }

  // 2. Procesar los IDs/nombres existentes en product.categoryIds (que vienen de rawProduct.categories)
  // Estos podrían ser ya IDs nuevos, o nombres antiguos que necesitan mapeo.
  currentIdsFromProduct.forEach(idOrName => {
    const mappedFromCurrent = categoryMapping[idOrName];
    if (mappedFromCurrent) { // Si es un nombre antiguo conocido en el mapping
      mappedFromCurrent.forEach(id => newIdsSet.add(id));
    } else { // Si no está en el mapping, asumir que es un ID nuevo (o un nombre antiguo no mapeable)
      newIdsSet.add(idOrName);
    }
  });
  
  // Si después de procesar originalCategory y currentIds, newIdsSet está vacío,
  // y originalCategory_from_json existe pero no se mapeó, añadirlo tal cual
  // como último recurso (podría ser un ID nuevo que no estaba en categoryMapping).
  if (newIdsSet.size === 0 && product.originalCategory_from_json && !categoryMapping[product.originalCategory_from_json]) {
    newIdsSet.add(product.originalCategory_from_json);
  }

  product.categoryIds = Array.from(newIdsSet);

  // 3. Actualizar el campo 'category' del producto a un valor representativo
  if (product.categoryIds.length > 0) {
    // Priorizar un ID que provenga del mapeo de originalCategory_from_json si existió y se mapeó
    if (product.originalCategory_from_json && categoryMapping[product.originalCategory_from_json]) {
      product.category = categoryMapping[product.originalCategory_from_json][0];
    } else {
      // Sino, usar el primer ID de la lista final de categoryIds
      product.category = product.categoryIds[0];
    }
  } else if (product.originalCategory_from_json) {
    // Si no hay categoryIds finales pero había una originalCategory, usarla como fallback
    product.category = product.originalCategory_from_json;
  } else {
    // Si no hay ninguna información de categoría, dejarlo vacío
    product.category = '';
  }

  return product;
}

// Ejemplo de uso (para demostración, lo eliminarías en producción aquí):
/*
const rawSampleProduct = {
  _id: "67bfd5f41e6fa9c1ff1a2694",
  name: "Botas de seguridad",
  description: "Botas con punta de acero para protección",
  price: 50,
  category: "Calzado",
  technicalSpecs: {
    "Características": "Botas de seguridad con puntera reforzada, suela antideslizante y material resistente al agua.",
    "Especificaciones": "Talla: 42, Material: Cuero sintético, Color: Negro.",
    "Presentación": "Caja de 1 par de botas."
  },
  documentation: "/path/to/technical-sheet.pdf"
};

const transformedProduct = transformRawProductToCatalogProduct(rawSampleProduct);
console.log("Transformed Product:", transformedProduct);

const productWithNewCategories = assignNewCategoryIds(transformedProduct);
console.log("Product with New Categories:", productWithNewCategories);
*/