import React, { useState } from 'react';
import './PersonalProtectionSection.css';
import { StaticImage } from './StaticImage';

interface Category {
  id: number;
  name: string;
  area: string;
  top: string;
  left: string;
}

const categories: Category[] = [
  { id: 1, name: '<PERSON><PERSON><PERSON>', area: 'head', top: '20%', left: '18%' },
  { id: 2, name: 'Guantes', area: 'hands', top: '63%', left: '82%' },
  { id: 3, name: 'Chalecos', area: 'torso', top: '63%', left: '18%' },
  { id: 4, name: '<PERSON>zad<PERSON>', area: 'feet', top: '85%', left: '82%' },
  { id: 5, name: 'Protección Ocular', area: 'head', top: '38%', left: '18%' },
  { id: 6, name: 'Protección Respiratoria', area: 'head', top: '20%', left: '82%' },
  { id: 7, name: 'Protección Auditiva', area: 'head', top: '42%', left: '82%' },
  { id: 8, name: 'Protección Facial', area: 'head', top: '46%', left: '18%' },
  { id: 9, name: 'Indumentaria', area: 'body', top: '84%', left: '18%' },
];

const PersonalProtectionSection = () => {
  const [hoveredCategory, setHoveredCategory] = useState<Category | null>(null);

  return (
    <div className="flex justify-center items-center w-full min-h-[50vh]">
      <section className="h-full">
        <div className="h-full">
          <h2 className="text-3xl font-bold text-gray-900 relative mb-5">
            Equipamiento de Protección Personal
            <span className="block h-1 w-24 bg-amber-500 mt-2 rounded-full"></span>
          </h2>
          <div className="image-container relative max-w-[400px] mx-auto">
            <StaticImage 
              name="epp2.png"
              alt="Elementos de Protección Personal"
              className="w-[40%] h-auto mx-auto"
              fallback="placeholder-product.jpg"
            />
            {categories.map((category) => (
              <div
                key={category.id}
                className="absolute w-[40px] h-[40px] bg-blue-500 opacity-15 cursor-pointer hotspot"
                style={{ top: category.top, left: category.left }}
                onMouseEnter={() => setHoveredCategory(category)}
                onMouseLeave={() => setHoveredCategory(null)}
              />
            ))}
            {hoveredCategory && (
              <div 
                className="absolute bg-white border p-1 shadow-md tooltip text-sm"
                style={{ top: hoveredCategory.top, left: `calc(${hoveredCategory.left} + 1rem)` }}
              >
                <p>{hoveredCategory.name}</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default PersonalProtectionSection;
