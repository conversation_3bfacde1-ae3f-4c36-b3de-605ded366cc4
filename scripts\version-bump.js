const fs = require('fs');
const path = require('path');

const PACKAGE_JSON_PATH = path.join(__dirname, '..', 'package.json');

// Tipos de versión
const VERSION_TYPES = {
  MAJOR: 'major', // X.0.0
  MINOR: 'minor', // 0.X.0
  PATCH: 'patch'  // 0.0.X
};

function bumpVersion(type) {
  const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, 'utf8'));
  const [major, minor, patch] = packageJson.version.split('.').map(Number);

  switch (type) {
    case VERSION_TYPES.MAJOR:
      packageJson.version = `${major + 1}.0.0`;
      break;
    case VERSION_TYPES.MINOR:
      packageJson.version = `${major}.${minor + 1}.0`;
      break;
    case VERSION_TYPES.PATCH:
      packageJson.version = `${major}.${minor}.${patch + 1}`;
      break;
    default:
      throw new Error('Invalid version type');
  }

  fs.writeFileSync(PACKAGE_JSON_PATH, JSON.stringify(packageJson, null, 2));
  console.log(`Version bumped to ${packageJson.version}`);
}

// Ejecutar desde línea de comandos
const type = process.argv[2]?.toLowerCase();
if (Object.values(VERSION_TYPES).includes(type)) {
  bumpVersion(type);
} else {
  console.error('Usage: node version-bump.js <major|minor|patch>');
  process.exit(1);
}