import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function updateImageUrls() {
  try {
    console.log('Starting image URL updates...');
    
    // 1. Obtener todos los productos
    const { data: products, error } = await supabase
      .from('products')
      .select('id, image_url');

    if (error) throw error;

    console.log(`Found ${products.length} products to process`);

    let updated = 0;
    let skipped = 0;

    for (const product of products) {
      try {
        // Verificar si la imagen ya está en el bucket
        const expectedPath = `${product.id}/main`;
        const { data: files, error: listError } = await supabase.storage
          .from('product-images')
          .list(product.id);

        if (listError) throw listError;

        // Si encontramos un archivo en el directorio del producto
        if (files && files.length > 0) {
          const file = files[0]; // Tomar el primer archivo
          const newPath = `${product.id}/${file.name}`;
          
          // Obtener la URL pública del bucket
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(newPath);

          // Actualizar la URL en la base de datos
          const { error: updateError } = await supabase
            .from('products')
            .update({ image_url: publicUrl })
            .eq('id', product.id);

          if (updateError) throw updateError;

          console.log(`✅ Updated URL for product ${product.id}`);
          updated++;
        } else {
          console.log(`⏭️ Skipping product ${product.id} - no files in storage`);
          skipped++;
        }
      } catch (err) {
        console.error(`❌ Error processing product ${product.id}:`, err.message);
        skipped++;
      }
    }

    console.log('\n=== Update Summary ===');
    console.log(`Total products processed: ${products.length}`);
    console.log(`URLs updated: ${updated}`);
    console.log(`Products skipped: ${skipped}`);

  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

updateImageUrls();