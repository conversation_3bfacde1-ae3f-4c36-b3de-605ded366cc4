import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './env';

export const STORAGE_CONFIG = {
  PRODUCT_BUCKET: 'product-images',
  STATIC_BUCKET: 'static-images',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
};

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Añadimos la función getPublicImageUrl que faltaba
export const getPublicImageUrl = (name: string, isStatic: boolean = false): string => {
  const bucket = isStatic ? STORAGE_CONFIG.STATIC_BUCKET : STORAGE_CONFIG.PRODUCT_BUCKET;
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(name);
  return publicUrl;
};

export const getProductImageUrl = async (productId: string, imageUrl?: string | null): Promise<string> => {
  // Si la URL ya es una URL completa de Supabase Storage, retornarla
  if (imageUrl?.includes('/storage/v1/object/public/')) {
    return imageUrl;
  }

  // Si es una ruta relativa al bucket
  if (imageUrl) {
    const { data: { publicUrl } } = supabase.storage
      .from(STORAGE_CONFIG.PRODUCT_BUCKET)
      .getPublicUrl(imageUrl);
    return publicUrl;
  }

  // Si no hay URL, intentar encontrar la imagen en el bucket del producto
  try {
    const { data: files } = await supabase.storage
      .from(STORAGE_CONFIG.PRODUCT_BUCKET)
      .list(productId);

    if (files && files.length > 0) {
      const { data: { publicUrl } } = supabase.storage
        .from(STORAGE_CONFIG.PRODUCT_BUCKET)
        .getPublicUrl(`${productId}/${files[0].name}`);
      return publicUrl;
    }
  } catch (error) {
    console.error('Error accessing storage:', error);
  }

  // Fallback a imagen por defecto
  const { data: { publicUrl } } = supabase.storage
    .from(STORAGE_CONFIG.STATIC_BUCKET)
    .getPublicUrl('placeholder-product.jpg');
  
  return publicUrl;
};

// Función auxiliar para verificar si una URL es accesible
export const verifyImageAccess = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};




