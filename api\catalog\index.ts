import { VercelRequest, VercelResponse } from '@vercel/node';
import { createClient } from '@supabase/supabase-js';
import cacheMiddleware from '../_middleware';

const supabase = createClient(
  process.env.VITE_SUPABASE_URL!,
  process.env.VITE_SUPABASE_ANON_KEY!
);

async function handler(req: VercelRequest, res: VercelResponse) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    try {
      const { industry, category, featured } = req.query;
      
      let query = supabase.from('products').select('*');
      
      // Aplicar filtros según los parámetros
      if (industry) {
        query = query.eq('industry', industry);
      }
      
      if (category) {
        query = query.eq('category', category);
      }
      
      if (featured === 'true') {
        query = query.eq('featured', true);
      }
      
      // Ordenar resultados
      query = query.order('name');
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      res.status(200).json(data || []);
    } catch (error) {
      console.error('Catalog API error:', error);
      res.status(500).json({ 
        error: 'Error fetching catalog',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'OPTIONS']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

// Aplicar middleware de caché
export default cacheMiddleware(handler);
