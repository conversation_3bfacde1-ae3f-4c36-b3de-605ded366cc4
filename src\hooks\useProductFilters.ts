import { useState, useEffect, useMemo, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CatalogProduct } from '../types/product';
import { getEPPSubcategoriesByCategory } from '../config/eppCategoryMapping';

export interface ProductFilters {
  searchTerm: string;
  selectedIndustries: string[];
  selectedCategories: string[];
  selectedSubcategories: string[];
  priceRange: [number, number];
  sortBy: 'name' | 'price' | 'newest';
  sortOrder: 'asc' | 'desc';
}

export const useProductFilters = (products: CatalogProduct[]) => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Inicializar filtros desde URL
  const [filters, setFilters] = useState<ProductFilters>(() => ({
    searchTerm: searchParams.get('search') || '',
    selectedIndustries: searchParams.get('industry')?.split(',').filter(Boolean) || [],
    selectedCategories: searchParams.get('category')?.split(',').filter(Boolean) || [],
    selectedSubcategories: searchParams.get('subcategory')?.split(',').filter(Boolean) || [],
    priceRange: [0, 10000],
    sortBy: (searchParams.get('sortBy') as any) || 'name',
    sortOrder: (searchParams.get('sortOrder') as any) || 'asc'
  }));

  // Función para actualizar filtros y URL
  const updateFilters = useCallback((newFilters: Partial<ProductFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Función específica para manejar selección de categorías EPP
  const handleEPPCategorySelect = useCallback((category: string, subcategory?: string) => {
    const newFilters: Partial<ProductFilters> = {
      selectedCategories: [category],
      selectedSubcategories: subcategory ? [subcategory] : []
    };
    updateFilters(newFilters);
  }, [updateFilters]);

  // Sincronizar filtros con URL
  useEffect(() => {
    const params = new URLSearchParams();
    
    if (filters.searchTerm) params.set('search', filters.searchTerm);
    if (filters.selectedIndustries.length) params.set('industry', filters.selectedIndustries.join(','));
    if (filters.selectedCategories.length) params.set('category', filters.selectedCategories.join(','));
    if (filters.selectedSubcategories.length) params.set('subcategory', filters.selectedSubcategories.join(','));
    if (filters.sortBy !== 'name') params.set('sortBy', filters.sortBy);
    if (filters.sortOrder !== 'asc') params.set('sortOrder', filters.sortOrder);
    
    setSearchParams(params, { replace: true });
  }, [filters, setSearchParams]);

  // Productos filtrados
  const filteredProducts = useMemo(() => {
    let result = [...products];

    // Filtro por término de búsqueda
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      result = result.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.description?.toLowerCase().includes(term) ||
        product.brand?.toLowerCase().includes(term)
      );
    }

    // Filtro por industrias
    if (filters.selectedIndustries.length > 0) {
      result = result.filter(product =>
        product.industries?.some(industry =>
          filters.selectedIndustries.includes(industry)
        )
      );
    }

    // Filtro por categorías (incluyendo EPP)
    if (filters.selectedCategories.length > 0) {
      result = result.filter(product => {
        // Verificar categorías directas
        const hasDirectCategory = filters.selectedCategories.some(category =>
          product.categories?.includes(category) ||
          product.category === category
        );

        // Verificar subcategorías EPP
        const hasEPPSubcategory = filters.selectedCategories.some(category => {
          const eppSubcategories = getEPPSubcategoriesByCategory(category);
          return product.subcategories?.some(sub => eppSubcategories.includes(sub));
        });

        return hasDirectCategory || hasEPPSubcategory;
      });
    }

    // Filtro por subcategorías específicas
    if (filters.selectedSubcategories.length > 0) {
      result = result.filter(product =>
        product.subcategories?.some(sub =>
          filters.selectedSubcategories.includes(sub)
        ) ||
        // Para productos EPP específicos por ID
        filters.selectedSubcategories.some(subId =>
          product.id === subId || product.sku === subId
        )
      );
    }

    // Filtro por rango de precios
    result = result.filter(product =>
      product.price >= filters.priceRange[0] && 
      product.price <= filters.priceRange[1]
    );

    // Ordenamiento
    result.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'newest':
          comparison = new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
          break;