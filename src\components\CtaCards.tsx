import React from 'react';
import { useNavigate } from 'react-router-dom';
import { LucideIcon, ArrowRight, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface CtaCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  path: string;
  buttonText: string;
  benefits: string[];
  benefitsTitle?: string; // Nueva propiedad para el título de los beneficios
  stats?: {
    label: string;
    value: string;
  };
  category: string;
}

const FullScreenCtaSection: React.FC<{cards?: CtaCardProps[]}> = ({ cards = [] }) => {
  const navigate = useNavigate();
  const [hoveredCard, setHoveredCard] = React.useState<number | null>(null);

  // Early return si no hay cards
  if (!cards || cards.length === 0) {
    return (
      <section className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">No hay elementos CTA disponibles</h2>
          <p className="text-slate-400">Configure las tarjetas CTA para mostrar contenido.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-[calc(100vh-60px)] bg-slate-900 flex items-center py-12">
      <div className="container mx-auto px-6 w-full">
        {/* Header Section */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Soluciones Industriales
              <span className="text-amber-500"> de Vanguardia</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Optimiza y asegura tu operación industrial con nuestra gama de productos certificados y confiables. 
              Tecnología probada para maximizar eficiencia y seguridad.
            </p>
          </motion.div>
        </div>

        {/* CTA Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {cards.map((card, index) => (
            <motion.div
              key={index}
              className="bg-slate-800 rounded-2xl overflow-hidden shadow-2xl border border-slate-700/50"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              onHoverStart={() => setHoveredCard(index)}
              onHoverEnd={() => setHoveredCard(null)}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="p-8">
                {/* Header con categoría y stats */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <motion.div
                      className="p-4 bg-slate-700/70 rounded-xl"
                      animate={{
                        backgroundColor: hoveredCard === index ? '#f59e0b' : 'rgb(51 65 85 / 0.7)',
                        scale: hoveredCard === index ? 1.05 : 1
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <card.icon 
                        className={`w-8 h-8 transition-colors duration-200 ${
                          hoveredCard === index ? 'text-slate-900' : 'text-amber-400'
                        }`} 
                      />
                    </motion.div>
                    <div>
                      <span className="text-xs font-bold text-amber-500 uppercase tracking-wider">
                        {card.category}
                      </span>
                      <h3 className="text-2xl font-bold text-white mt-1">
                        {card.title}
                      </h3>
                    </div>
                  </div>
                  
                  {card.stats && (
                    <div className="text-right">
                      <div className="text-2xl font-bold text-amber-500">
                        {card.stats.value}
                      </div>
                      <div className="text-xs text-slate-400 uppercase tracking-wide">
                        {card.stats.label}
                      </div>
                    </div>
                  )}
                </div>

                {/* Descripción */}
                <p className="text-slate-300 text-lg leading-relaxed mb-6">
                  {card.description} {/* Texto proviene de FeatureCards.ts */}
                </p>

                {/* Beneficios */}
                <div className="space-y-3 mb-8">
                  <h4 className="text-sm font-bold text-amber-500 uppercase tracking-wider">
                    {card.benefitsTitle || "Velocidad - Atención Especial - Calidad - Confiabilidad"} 
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {(card.benefits || []).map((benefit, benefitIndex) => (
                      <motion.div
                        key={benefitIndex}
                        className="flex items-center gap-3"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: (index * 0.1) + (benefitIndex * 0.05) }}
                      >
                        <CheckCircle className="w-5 h-5 text-amber-500 flex-shrink-0" />
                        <span className="text-slate-300 text-sm">{benefit}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <motion.button
                  onClick={() => navigate(card.path)}
                  className="w-full bg-slate-700 hover:bg-amber-500 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-lg">{card.buttonText}</span>
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </motion.button>
              </div>

              {/* Decorative accent */}
              <motion.div
                className="h-1 bg-gradient-to-r from-amber-500 to-amber-600"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: hoveredCard === index ? 1 : 0.3 }}
                transition={{ duration: 0.3 }}
                style={{ originX: 0 }}
              />
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <p className="text-slate-400 text-lg mb-4">
            ¿Necesitas una solución personalizada?
          </p>
          <button className="bg-amber-500 hover:bg-amber-600 text-slate-900 font-bold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
            Contactar Especialista
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default FullScreenCtaSection;