import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';
import { QueryClient } from '@tanstack/react-query';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validación de variables de entorno
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables. Please check your .env file');
  console.error('Required variables:');
  console.error('VITE_SUPABASE_URL:', supabaseUrl ? '✓' : '✗');
  console.error('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '✓' : '✗');
  throw new Error('Missing Supabase environment variables');
}

// Validar formato de URL
const isValidUrl = (urlString: string): boolean => {
  try {
    new URL(urlString);
    return true;
  } catch (e) {
    return false;
  }
};

import { SupabaseClient } from '@supabase/supabase-js'; // Add this import if not already present at the top

let supabase: SupabaseClient<Database> | null = null;
let checkSupabaseConnection = async () => false;
let setupSupabaseListener = (_: QueryClient) => () => {};

if (!isValidUrl(supabaseUrl)) {
  console.error('Invalid Supabase URL:', supabaseUrl);
  console.error('Please ensure VITE_SUPABASE_URL is a valid URL in your .env file');
  throw new Error('Invalid Supabase URL configuration');
} else {
  supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

  checkSupabaseConnection = async () => {
    try {
      const { data, error } = await supabase! // Assert supabase is not null
        .from('products')
        .select('count')
        .single();
      
      if (error) throw error;
      console.log('🟢 Supabase connection successful');
      return true;
    } catch (error) {
      console.error('🔴 Supabase connection failed:', error);
      return false;
    }
  };

  setupSupabaseListener = (queryClient: QueryClient) => {
    const channel = supabase! // Assert supabase is not null
      .channel('product-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'products'
        },
        (payload) => {
          console.log('Change received!', payload);
          queryClient.invalidateQueries({ queryKey: ['products'] });
        }
      )
      .subscribe();

    return () => {
      supabase!.removeChannel(channel); // Assert supabase is not null
    };
  };
}

export { supabase, checkSupabaseConnection, setupSupabaseListener };

