// Backend API Configuration (Vercel)
const getApiUrl = () => {
  // Desarrollo local
  if (import.meta.env.DEV) {
    return 'http://localhost:3010';
  }
  
  // Producción (Vercel)
  return 'https://cr-work.vercel.app';
};

export const API_URL = import.meta.env.VITE_API_URL || getApiUrl();

// Supabase Configuration
export const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
export const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;
