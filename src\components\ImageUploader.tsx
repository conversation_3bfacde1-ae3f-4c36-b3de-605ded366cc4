import React, { useState } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { STORAGE_CONFIG } from '../config/storage';
import { uploadProductImage, validateImage } from '../lib/imageStorage';

interface ImageUploaderProps {
  currentImageUrl?: string;
  productId: string;
  onImageUpdate: (newUrl: string) => void;
  onError: (error: string) => void;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  currentImageUrl,
  productId,
  onImageUpdate,
  onError
}) => {
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      // Validar antes de mostrar preview
      await validateImage(file);
      setPreview(URL.createObjectURL(file));
    } catch (error) {
      onError(error.message);
      event.target.value = '';
    }
  };

  const handleUpload = async () => {
    const fileInput = document.querySelector<HTMLInputElement>('#product-image');
    const file = fileInput?.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      const { publicUrl } = await uploadProductImage(file, productId, currentImageUrl);
      onImageUpdate(publicUrl);
      setPreview(null);
      if (fileInput) fileInput.value = '';
    } catch (error) {
      onError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelPreview = () => {
    setPreview(null);
    const fileInput = document.querySelector<HTMLInputElement>('#product-image');
    if (fileInput) fileInput.value = '';
  };

  return (
    <div className="space-y-4">
      {/* Área de imagen actual o preview */}
      <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
        {(currentImageUrl || preview) ? (
          <div className="relative">
            <img
              src={preview || currentImageUrl}
              alt="Product preview"
              className="max-h-64 mx-auto object-contain"
            />
            {preview && (
              <div className="absolute top-0 right-0 p-2 space-x-2">
                <button
                  onClick={handleUpload}
                  disabled={loading}
                  className="bg-green-500 text-white p-2 rounded-full hover:bg-green-600"
                >
                  <Upload size={16} />
                </button>
                <button
                  onClick={handleCancelPreview}
                  className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="py-8">
            <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">
              Arrastra una imagen o haz clic para seleccionar
            </p>
          </div>
        )}
      </div>

      {/* Input file oculto */}
      <input
        type="file"
        id="product-image"
        accept={STORAGE_CONFIG.ALLOWED_TYPES.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Botón para activar selección de archivo */}
      <button
        type="button"
        onClick={() => document.getElementById('product-image')?.click()}
        disabled={loading}
        className="w-full py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {loading ? 'Subiendo...' : 'Seleccionar imagen'}
      </button>

      <p className="text-xs text-gray-500">
        Formatos permitidos: JPG, PNG, WebP. Tamaño máximo: 5MB
      </p>
    </div>
  );
};