import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

interface ImageUploadResult {
  publicUrl: string;
  path: string;
}

export const uploadProductImage = async (
  file: File,
  productId: string,
  oldImageUrl?: string
): Promise<ImageUploadResult> => {
  try {
    // 1. Generar nombre consistente para la imagen
    const fileExt = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const fileName = `main.${fileExt}`; // Nombre consistente para la imagen principal
    const finalPath = `${productId}/${fileName}`;

    // 2. Si existe una imagen anterior, eliminarla
    if (oldImageUrl) {
      const oldPath = oldImageUrl.split('/').slice(-2).join('/'); // Obtiene "productId/filename"
      if (oldPath) {
        await supabase.storage
          .from('product-images')
          .remove([oldPath]);
      }
    }

    // 3. Subir la nueva imagen directamente a product-images
    const { error: uploadError } = await supabase.storage
      .from('product-images')
      .upload(finalPath, file, {
        upsert: true, // Sobreescribir si existe
        contentType: `image/${fileExt}`
      });

    if (uploadError) throw uploadError;

    // 4. Obtener URL pública
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(finalPath);

    return {
      publicUrl,
      path: finalPath
    };
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Función auxiliar para validar imagen antes de subir
export const validateImage = (file: File): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      reject(new Error('Tipo de archivo no válido. Use JPG, PNG o WebP'));
    }

    if (file.size > maxSize) {
      reject(new Error('Imagen demasiado grande. Máximo 5MB'));
    }

    const img = new Image();
    img.onload = () => {
      URL.revokeObjectURL(img.src);
      resolve(true);
    };
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('Archivo no es una imagen válida'));
    };
    img.src = URL.createObjectURL(file);
  });
};
