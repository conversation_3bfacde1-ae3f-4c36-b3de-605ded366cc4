import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Usar SERVICE_ROLE_KEY en lugar de ANON_KEY
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function migrateImages() {
  try {
    console.log('Starting image migration...');
    
    // 1. Obtener todos los productos
    const { data: products, error } = await supabase
      .from('products')
      .select('id, image_url');

    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }

    console.log(`Found ${products.length} products to process`);

    let successful = 0;
    let failed = 0;

    for (const product of products) {
      if (!product.image_url) {
        console.log(`Skipping product ${product.id} - no image URL`);
        continue;
      }

      try {
        console.log(`Processing product ${product.id}...`);
        
        // 2. <PERSON><PERSON><PERSON> imagen actual
        const response = await fetch(product.image_url);
        if (!response.ok) throw new Error(`Failed to fetch image: ${response.statusText}`);
        
        const blob = await response.blob();
        const fileExt = product.image_url.split('.').pop()?.toLowerCase() || 'jpg';
        
        // 3. Subir con nuevo formato de nombre
        const newPath = `${product.id}/main.${fileExt}`;
        const { error: uploadError, data: uploadData } = await supabase.storage
          .from('product-images')
          .upload(newPath, blob, {
            upsert: true,
            contentType: `image/${fileExt}`
          });

        if (uploadError) throw uploadError;

        // 4. Obtener URL pública
        const { data: { publicUrl } } = supabase.storage
          .from('product-images')
          .getPublicUrl(newPath);

        // 5. Actualizar URL en la base de datos
        const { error: updateError } = await supabase
          .from('products')
          .update({ image_url: publicUrl })
          .eq('id', product.id);

        if (updateError) throw updateError;

        console.log(`✅ Successfully migrated image for product ${product.id}`);
        successful++;
      } catch (err) {
        console.error(`❌ Error migrating image for product ${product.id}:`, err.message);
        failed++;
      }
    }

    console.log('\n=== Migration Summary ===');
    console.log(`Total products processed: ${products.length}`);
    console.log(`Successful migrations: ${successful}`);
    console.log(`Failed migrations: ${failed}`);
  } catch (error) {
    console.error('Fatal migration error:', error);
    process.exit(1);
  }
}

migrateImages();
