import { ImageUploader } from './ImageUploader';
import { toast } from 'react-toastify';

const ProductForm: React.FC<Props> = ({ product, onSubmit }) => {
  const [formData, setFormData] = useState({
    // ... otros campos ...
    image_url: product?.image_url || ''
  });

  const handleImageUpdate = (newUrl: string) => {
    setFormData(prev => ({ ...prev, image_url: newUrl }));
    toast.success('Imagen actualizada correctamente');
  };

  const handleImageError = (error: string) => {
    toast.error(error);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* ... otros campos del formulario ... */}
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700">
          Imagen del producto
        </label>
        <ImageUploader
          currentImageUrl={formData.image_url}
          productId={product?.id || 'new'}
          onImageUpdate={handleImageUpdate}
          onError={handleImageError}
        />
      </div>

      {/* ... resto del formulario ... */}
    </form>
  );
};
