const VERCEL_URL = 'https://cr-work.vercel.app/'; // Reemplaza con tu URL

const testEndpoints = async () => {
  const endpoints = [
    '/api/health',
    '/api/products',
    '/api/catalog',
    '/api/catalog?industry=CONSTRUCCION'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🧪 Testing ${endpoint}...`);
      const response = await fetch(`${VERCEL_URL}${endpoint}`);
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${endpoint}: OK`);
        console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
      } else {
        console.log(`❌ ${endpoint}: ${response.status} - ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint}: Error - ${error}`);
    }
    console.log('---');
  }
};

testEndpoints();