import React, { useState, useEffect } from 'react';
import { getProductImageUrl, verifyImageAccess } from '../config/storage';

interface ProductImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  productId: string;
  imageUrl?: string;
}

export const ProductImage: React.FC<ProductImageProps> = ({ 
  productId, 
  imageUrl, 
  className = '', 
  alt = '', 
  ...props 
}) => {
  const [src, setSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        const url = await getProductImageUrl(productId, imageUrl);
        console.log(`Loading image for product ${productId}:`, url);
        
        // Verificar si la URL es accesible
        const isAccessible = await verifyImageAccess(url);
        if (!isAccessible) {
          console.warn(`Image not accessible: ${url}`);
        }
        
        setSrc(url);
      } catch (error) {
        console.error('Error loading product image:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [productId, imageUrl]);

  if (isLoading) {
    return <div className={`animate-pulse bg-gray-200 ${className}`} />;
  }

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      onError={(e) => {
        console.warn(`Image failed to load: ${src}`);
        // Intentar cargar la imagen por defecto
        const defaultImage = supabase.storage
          .from(STORAGE_CONFIG.STATIC_BUCKET)
          .getPublicUrl('placeholder-product.jpg')
          .data.publicUrl;
        e.currentTarget.src = defaultImage;
      }}
      {...props}
    />
  );
};
