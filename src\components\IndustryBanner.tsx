import React from 'react';
import { getPublicImageUrl } from '../config/storage';

interface IndustryBannerProps {
  selectedIndustry: string | null;
  selectedIndustriesCount?: number; // Nuevo prop para contar industrias seleccionadas
}

const IndustryBanner: React.FC<IndustryBannerProps> = ({ 
  selectedIndustry, 
  selectedIndustriesCount = 0 
}) => {
  const getBannerImage = () => {
    // INDUSTRY SELECTOR REMOVED: Always show default banner instead of industry-specific banners
    return getPublicImageUrl('banners/default-banner.jpeg', true);

    // COMMENTED OUT: Previous logic that showed industry-specific banners
    // const bannerMap: { [key: string]: string } = {
    //   'MINERIA': 'banners/mineria-banner.jpg',
    //   'CONSTRUCCION': 'banners/construccion-banner.jpg',
    //   'MANUFACTURA': 'banners/manufactura-banner.jpg',
    //   'PETROLERA': 'banners/petrolera-banner.jpg',
    //   'QUIMICA': 'banners/quimica-banner.jpg',
    //   'METALMECANICA': 'banners/metalmecanica-banner.jpg',
    //   'LOGISTICA': 'banners/logistica-banner.jpg',
    //   'SALUD': 'banners/salud-banner.jpg',
    //   'AGRICOLA': 'banners/agricola-banner.jpg',
    //   'ALIMENTICIA': 'banners/alimenticia-banner.jpg'
    // };

    // if (!selectedIndustry || !bannerMap[selectedIndustry]) {
    //   return getPublicImageUrl('banners/default-banner.jpeg', true);
    // }

    // return getPublicImageUrl(bannerMap[selectedIndustry], true);
  };

  const getBannerTitle = () => {
    // INDUSTRY SELECTOR REMOVED: Always show generic title instead of specific industry names
    return 'Catálogo Completo';

    // COMMENTED OUT: Previous logic that showed specific industry names
    // if (selectedIndustriesCount > 1) {
    //   return 'Varias Industrias';
    // }
    // return selectedIndustry || 'Catálogo Completo';
  };

  const bannerImage = getBannerImage();

  return (
    <div className="w-full h-36 mb-2 rounded-xl overflow-hidden shadow-md mt-4">
      <div className="relative w-full h-full">
        <img
          src={bannerImage}
          alt={getBannerTitle()}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50" />
        <div className="absolute inset-0 flex items-center justify-center">
          <h2 className="text-2xl font-bold text-white drop-shadow-lg">
            {getBannerTitle()}
          </h2>
        </div>
      </div>
    </div>
  );
};

export default IndustryBanner;












