import React, { useState } from 'react';
import { CartItem } from '../types';
import { ArrowLeft, FileText, Send } from 'lucide-react';
import { FEATURE_FLAGS } from '../config/featureFlags';
import { useNavigate } from 'react-router-dom';
import QuoteRequestModal from './QuoteRequestModal';

interface OrderDetailProps {
    items: CartItem[];
}

const OrderDetail: React.FC<OrderDetailProps> = ({ items }) => {
    const navigate = useNavigate();
    const [showQuoteModal, setShowQuoteModal] = useState(false);

    const handleQuoteRequest = (data: { fullName: string; company: string; email: string }) => {
        console.log('Quote request data:', data);
        console.log('Cart items:', items);
        // Aquí iría la lógica para procesar la cotización
        setShowQuoteModal(false);
        navigate('/'); // O donde corresponda después de enviar la cotización
    };

    const calculateItemTotals = (item: CartItem) => {
        const basePrice = parseFloat(item.price);
        const subtotal = basePrice * item.quantity;
        const iva = subtotal * 0.21; // 21% IVA
        const discount = 0; // Implementar lógica de descuentos aquí
        return {
            subtotal,
            iva,
            discount,
            total: subtotal + iva - discount
        };
    };

    const totals = items.reduce((acc, item) => {
        const itemTotals = calculateItemTotals(item);
        return {
            subtotal: acc.subtotal + itemTotals.subtotal,
            iva: acc.iva + itemTotals.iva,
            discount: acc.discount + itemTotals.discount,
            total: acc.total + itemTotals.total
        };
    }, { subtotal: 0, iva: 0, discount: 0, total: 0 });

    return (
        <div 
            className="bg-gray-50"
            style={{
                minHeight: 'calc(100vh - var(--navbar-height))',
                marginTop: 'var(--navbar-height)',
                padding: '1.5rem'
            }}
        >
            <div className="max-w-7xl mx-auto">
                <div className="flex gap-6">
                    {/* Columna izquierda - Detalles de la orden */}
                    <div className="flex-1 bg-white rounded-lg shadow">
                        <div className="p-6 border-b flex items-center">
                            <button
                                onClick={() => navigate(-1)}
                                className="mr-4 text-gray-600 hover:text-gray-800"
                            >
                                <ArrowLeft className="w-6 h-6" />
                            </button>
                            <h1 className="text-2xl font-bold text-gray-800">
                                {FEATURE_FLAGS.SHOW_ORDER_DETAILS ? 'Pedido Detallado' : 'Resumen de Cotización'}
                            </h1>
                        </div>
                        
                        <div className="p-6">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="bg-gray-50">
                                            <th className="px-6 py-3 text-left text-sm font-semibold text-gray-600">Producto</th>
                                            <th className="px-6 py-3 text-left text-sm font-semibold text-gray-600">Marca</th>
                                            <th className="px-6 py-3 text-center text-sm font-semibold text-gray-600">Cantidad</th>
                                            {FEATURE_FLAGS.SHOW_ORDER_DETAILS && (
                                                <>
                                                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-600">Precio Unit.</th>
                                                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-600">Subtotal</th>
                                                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-600">IVA (21%)</th>
                                                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-600">Descuento</th>
                                                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-600">Total</th>
                                                </>
                                            )}
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {items.map((item) => {
                                            const itemTotals = calculateItemTotals(item);
                                            return (
                                                <tr key={item._id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 text-sm text-gray-900">{item.name}</td>
                                                    <td className="px-6 py-4 text-sm text-gray-600">{item.brand}</td>
                                                    <td className="px-6 py-4 text-sm text-center text-gray-900">{item.quantity}</td>
                                                    {FEATURE_FLAGS.SHOW_ORDER_DETAILS && (
                                                        <>
                                                            <td className="px-6 py-4 text-sm text-right text-gray-900">
                                                                ${Number(item.price).toFixed(2)}
                                                            </td>
                                                            <td className="px-6 py-4 text-sm text-right text-gray-900">
                                                                ${itemTotals.subtotal.toFixed(2)}
                                                            </td>
                                                            <td className="px-6 py-4 text-sm text-right text-gray-600">
                                                                ${itemTotals.iva.toFixed(2)}
                                                            </td>
                                                            <td className="px-6 py-4 text-sm text-right text-green-600">
                                                                ${itemTotals.discount.toFixed(2)}
                                                            </td>
                                                            <td className="px-6 py-4 text-sm text-right font-medium text-blue-600">
                                                                ${itemTotals.total.toFixed(2)}
                                                            </td>
                                                        </>
                                                    )}
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>

                            {FEATURE_FLAGS.SHOW_ORDER_DETAILS && (
                                <div className="mt-8 border-t pt-6">
                                    <div className="max-w-sm ml-auto space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Subtotal</span>
                                            <span className="text-gray-900">${totals.subtotal.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">IVA (21%)</span>
                                            <span className="text-gray-900">${totals.iva.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-gray-600">Descuentos</span>
                                            <span className="text-green-600">-${totals.discount.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between text-base font-semibold pt-4 border-t">
                                            <span className="text-gray-900">Total</span>
                                            <span className="text-gray-900">${totals.total.toFixed(2)}</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Columna derecha - Acciones */}
                    <div className="w-80">
                        <div className="bg-white rounded-lg shadow p-6 space-y-4">
                            <h2 className="text-lg font-semibold text-gray-900">Acciones</h2>
                            
                            <button 
                                onClick={() => {
                                    if (FEATURE_FLAGS.SHOW_ORDER_DETAILS) {
                                        // TO-DO: Implementar lógica para emitir orden
                                        console.log('Emitir orden - Implementación pendiente');
                                    } else {
                                        setShowQuoteModal(true);
                                    }
                                }}
                                className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition duration-300"
                            >
                                <Send className="w-5 h-5" />
                                {FEATURE_FLAGS.SHOW_ORDER_DETAILS ? 'Emitir Orden' : 'Solicitar Cotización'}
                            </button>
                            
                            <button className="w-full flex items-center justify-center gap-2 bg-gray-100 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-200 transition duration-300">
                                <FileText className="w-5 h-5" />
                                Descargar PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OrderDetail;
