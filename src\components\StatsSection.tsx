import React from 'react';
import { Users, Calendar, Package } from 'lucide-react';

const StatsSection = () => {
  const stats = [
    {
      title: 'Clientes Satisfechos',
      value: '800+',
      icon: Users,
      description: 'Empresas que confían en nosotros'
    },
    {
      title: 'Años de Experiencia',
      value: '15+',
      icon: Calendar,
      description: 'Brindando productos para todas las necesidades en la industria'
    },
    {
      title: 'Productos Disponibles',
      value: '2.000+',
      icon: Package,
      description: 'Para todas las necesidades. Visitá nuestro catálogo.'
    }
  ];

  return (
    <section className="bg-gray-100 pt-3 pb-8">
      <div className="container mx-auto px-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {stats.map((stat, index) => (
          <div 
            key={index} 
            className="bg-white rounded-lg shadow-lg p-8 text-center transform transition-transform duration-300 hover:-translate-y-2 relative"
            style={{ transform: 'scale(0.8)', transformOrigin: 'center' }}
          >
            <div className="flex justify-center mb-4">
              <div className="bg-amber-500 p-4 rounded-full">
                <stat.icon className="w-8 h-8 text-white" />
              </div>
            </div>
            <h3 className="text-4xl font-bold text-gray-800 mb-2">{stat.value}</h3>
            <h4 className="text-xl font-semibold text-gray-700 mb-2">{stat.title}</h4>
            <p className="text-gray-600">{stat.description}</p>
            {stat.title === 'Productos Disponibles' && (
              <a
                href="/files/CATALOGO_CRW.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="absolute top-[-20px] right-[-20px] bg-gradient-to-br from-blue-600 to-amber-500 text-white w-18 h-18 flex items-center justify-center rounded-full shadow-2xl border-4 border-white hover:scale-110 transition z-10 group"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v12m0 0l-4-4m4 4l4-4M4 20h16" />
                </svg>
                <span className="absolute left-1/2 top-full mt-2 -translate-x-1/2 bg-gray-900 text-white text-sm rounded px-3 py-2 opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap shadow-lg z-20">
                  Descargar Brochure
                </span>
              </a>
            )}
          </div>
        ))}
      </div>
      </div>
    </section>
  );
};

export default StatsSection;
