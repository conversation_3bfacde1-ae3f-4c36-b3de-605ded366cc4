import React from 'react';
import { StaticImage } from './StaticImage';
import { Shield, Award, Users, Warehouse } from 'lucide-react'; // Asegúrate de que Warehouse esté importado
import { motion } from 'framer-motion';

// Animation variants for sections/elements
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const ValueCard = ({ icon: Icon, title, description, featured = false }: { icon: React.ElementType, title: string, description: string, featured?: boolean }) => (
  <motion.div
    variants={fadeIn}
    className={`
      rounded-2xl p-8 flex flex-col h-full
      transition-all duration-300 transform hover:-translate-y-2
      border ${featured ? 'border-amber-300 bg-amber-50 shadow-amber-500/10' : 'border-gray-200 bg-white'}
      hover:shadow-xl
    `}
  >
    <div className="flex items-center gap-4 mb-4">
      <div className={`p-3 rounded-lg ${featured ? 'bg-amber-100' : 'bg-gray-100'}`}>
        <Icon className={`w-8 h-8 ${featured ? 'text-amber-600' : 'text-amber-500'}`} />
      </div>
      <h3 className="text-xl font-bold text-slate-800">{title}</h3>
    </div>
    <p className="text-slate-600 flex-grow">
      {description}
    </p>
  </motion.div>
);

const Nosotros: React.FC = () => {
  return (
    <div className="bg-white text-slate-800 pt-[60px] md:pt-[72px]">
      
      {/* Section 1: Hero Header */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 text-center">
          <motion.h1 
            variants={fadeIn}
            initial="hidden"
            animate="visible"
            className="text-4xl md:text-5xl font-bold text-slate-900 mb-4"
          >
            Nuestra <span className="text-amber-500">Historia</span>
          </motion.h1>
          <motion.div
            variants={fadeIn}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.2 }}
          >
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto">
              Un viaje de más de una década, forjado en el compromiso con la calidad y la seguridad integral para la industria.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Section 2: History Details */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Text Content */}
            <motion.div 
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.8 }}
              className="lg:w-2/3 space-y-6 text-lg text-slate-700 leading-relaxed"
            >
              <p>
                CR Work nace como resultado de la visión emprendedora de sus fundadores, 
                quienes identificaron la necesidad de brindar soluciones integrales en seguridad industrial 
                con un enfoque personalizado y profesional.
              </p>
              <p>
                Con más de una década de experiencia en el mercado, nos hemos consolidado como un 
                referente en la provisión de equipos de protección personal y seguridad industrial, 
                sirviendo a empresas de diversos sectores en toda la región.
              </p>
              <p>
                Nuestro compromiso con la calidad se refleja en la cuidadosa selección de las marcas 
                que representamos, trabajando únicamente con los fabricantes más reconocidos del mercado.
              </p>
              <p>
                Nos mueve el compromiso de generar relaciones de largo plazo, priorizando siempre la satisfacción y confianza de nuestros clientes.
                Escuchamos sus necesidades y adaptamos nuestras soluciones para brindar un servicio cercano, eficiente y confiable.
              </p>
            </motion.div>
            
            {/* Main Warehouse Image */}
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.8 }}
              className="lg:w-1/3 rounded-2xl overflow-hidden shadow-xl"
            >
              <StaticImage
                name="Frente3.jpeg"
                alt="Frente de CR Work"
                className="w-full h-full object-cover"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Section 3: Our Support / Facilities */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeIn}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">Nuestro Soporte</h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Contamos con instalaciones y un equipo preparado para responder a las demandas de nuestros clientes con agilidad y eficiencia.
            </p>
          </motion.div>
          
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            {/* Image 1 */}
            <motion.div variants={fadeIn} className="rounded-2xl overflow-hidden shadow-lg aspect-w-4 aspect-h-3 group">
              <StaticImage
                name="team1.jpg"
                alt="Equipo CR Work"
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
              />
            </motion.div>

            {/* Image 2 */}
            <motion.div variants={fadeIn} className="rounded-2xl overflow-hidden shadow-lg aspect-w-4 aspect-h-3 group">
              <StaticImage
                name="deposito-generico.jpg"
                alt="Nuestro Depósito"
                className="w-full h-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
              />
            </motion.div>

            {/* Image 3 Placeholder */}
            <motion.div variants={fadeIn} className="rounded-2xl overflow-hidden shadow-lg aspect-w-4 aspect-h-3 bg-gray-200 flex flex-col items-center justify-center text-gray-500">
              <Warehouse className="w-16 h-16 mb-4" />
              <span className="font-semibold">Logística Eficiente</span>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Section 4: Our Values */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeIn}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Nuestros <span className="text-amber-500">Valores</span>
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Los pilares que guían cada una de nuestras acciones y decisiones.
            </p>
          </motion.div>
          
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            <ValueCard
              icon={Award}
              title="Calidad"
              description="Trabajamos exclusivamente con las marcas más reconocidas del mercado, asegurando productos de la más alta calidad."
            />
            <ValueCard
              icon={Shield}
              title="Compromiso"
              description="Nos dedicamos a proporcionar las mejores soluciones en seguridad industrial, garantizando la satisfacción total de nuestros clientes."
              featured={true}
            />
            <ValueCard
              icon={Users}
              title="Servicio"
              description="Brindamos asesoramiento personalizado y atención dedicada para encontrar las soluciones que mejor se adapten a cada cliente."
            />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Nosotros;
