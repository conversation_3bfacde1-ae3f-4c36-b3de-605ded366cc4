
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ppeCategories, PPECategory, PPESubcategory } from '../config/ppeCategories';
import { ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase'; // Importa la instancia de Supabase
import './PPECategoryVisualizer.css';

// Construye la URL base del bucket usando la URL del proyecto y la ruta al bucket
const SUPABASE_CATEGORY_IMAGE_BASE_URL = `${supabase.storageUrl}/object/public/static-images/industry-selector/`;

const PPECategoryVisualizer: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<PPECategory | null>(null);
  const [hoveredSubcategory, setHoveredSubcategory] = useState<PPESubcategory | null>(null);
  const navigate = useNavigate();

  // Set the first category as default when component mounts
  useEffect(() => {
    if (ppeCategories.length > 0 && !selectedCategory) {
      setSelectedCategory(ppeCategories[0]);
    }
  }, []);

  const handleCategorySelect = (category: PPECategory) => {
    setSelectedCategory(category);
    setHoveredSubcategory(null);
  };

  // Función para navegar al catálogo con filtros aplicados
  const navigateToCatalog = (subcategory: PPESubcategory) => {
    if (!selectedCategory) return;
    
    console.log("Navegando al catálogo con:", {
      category: selectedCategory.name,
      subcategory: subcategory.name
    });
    
    // Navegar a la página de catálogo con los parámetros
    navigate(`/catalog?category=${encodeURIComponent(selectedCategory.id)}&subcategory=${encodeURIComponent(subcategory.id)}`);
  };

  // Función para navegar a todos los productos
  const navigateToAllProducts = () => {
    navigate('/catalog');
  };

  return (
    <section className="w-full bg-slate-900 text-slate-100 py-14 min-h-[calc(100vh-60px)]">
      <div className="container mx-auto px-4 md:px-8 flex flex-col h-full">
        <div className="text-center mb-6">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Equipos de Protección
            <span className="text-amber-500"> Personal</span>
          </h2>
        </div>

        {/* Category Selector - Single row with compact design - No scrollbar */}
        <div className="mb-6">
          <div className="category-icons-grid no-scrollbar">
            {ppeCategories.map((category) => {
              const Icon = category.icon;
              const isActive = selectedCategory?.id === category.id;
              
              return (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category)}
                  className={`
                    category-icon-button py-2
                    ${isActive
                      ? 'bg-amber-500 text-slate-900 shadow-md scale-105'
                      : 'bg-slate-700 text-slate-300 hover:bg-slate-600 hover:scale-102'}
                  `}
                  aria-pressed={isActive}
                  aria-label={`Seleccionar categoría ${category.name}`}
                >
                  <div className={`
                    category-icon-container p-1
                    ${isActive ? 'bg-amber-400' : 'bg-slate-600'}
                  `}>
                    <Icon className={`category-icon ${isActive ? 'text-slate-900' : 'text-amber-400'}`} />
                  </div>
                  <span className={`text-xs font-medium text-center truncate ${isActive ? 'text-slate-900' : 'text-slate-300'}`}>
                    {category.id === 'craneana' ? 'Cabeza' :
                     category.id === 'ocular' ? 'Ojos' :
                     category.id === 'facial' ? 'Facial' :
                     category.id === 'respiratoria' ? 'Respiración' :
                     category.id === 'indumentaria' ? 'Indumentaria' :
                     category.id === 'manos' ? 'Manos' :
                     category.id === 'calzado' ? 'Calzado' :
                     category.id === 'auditiva' ? 'Oídos' :
                     category.id === 'contra-fuego' ? 'Fuego' :
                     category.id === 'altura' ? 'Altura' :
                     category.name.split(' ').slice(-1)[0]}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Container - Using items-stretch to ensure equal height */}
        <div className="flex flex-col md:flex-row md:justify-center md:items-stretch gap-6 flex-grow">
          {/* Worker Image - Left panel - Changes based on selected category */}
          <div className="w-full md:w-1/2 lg:w-2/6 worker-image-container sticky-worker-image fixed-panel-height">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedCategory?.id || 'default'}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="w-full h-full"
              >

                <img
                  src={selectedCategory
                    ? `${SUPABASE_CATEGORY_IMAGE_BASE_URL}body-${selectedCategory.id}.jpg` // Cargar desde Supabase
                    : "/images/placeholder-product.jpg"} // Placeholder local si no hay categoría
                  alt={selectedCategory
                    ? `Trabajador con ${selectedCategory.name}`
                    : "Trabajador con equipos de protección personal"}
                  className="worker-image"
                  onError={(e) => {
                    // Fallback a un placeholder genérico si la imagen específica falla
                    const placeholderSrc = "/images/placeholder-product.jpg";
                    // Evitar loop infinito si el placeholder también falla
                    if (!e.currentTarget.src.endsWith(placeholderSrc)) {
                      e.currentTarget.src = placeholderSrc;
                    }
                  }}
                />
              </motion.div>
            </AnimatePresence>
          </div>


          {/* Products Panel - Expanded on right, height aligned with worker image */}
          <div
            className="w-full md:w-1/2 lg:w-2/4 bg-slate-800 rounded-xl p-6 shadow-lg flex flex-col fixed-panel-height"
          >
            {selectedCategory && (
              <>
                <h3 className="text-xl font-bold text-amber-500 mb-2">
                  {selectedCategory.name}
                </h3>
                <p className="text-slate-300 mb-4 text-sm">
                  {selectedCategory.description}
                </p>
                
                <div className="flex-grow overflow-y-auto no-scrollbar">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 subcategories-grid">
                    {selectedCategory.subcategories.map((subcategory) => {
                      const isHighlighted = hoveredSubcategory?.id === subcategory.id;
                      
                      return (
                        <motion.div
                          key={subcategory.id}
                          className={`
                            p-3 rounded-md flex items-center transition-all duration-200 cursor-pointer subcategory-card
                            ${isHighlighted
                              ? 'bg-amber-500 shadow-lg scale-102'
                              : 'bg-slate-700 hover:bg-slate-600'}
                          `}
                          animate={{
                            y: isHighlighted ? -1 : 0,
                          }}
                          transition={{ type: 'spring', stiffness: 300 }}
                          onMouseEnter={() => setHoveredSubcategory(subcategory)}
                          onMouseLeave={() => setHoveredSubcategory(null)}
                          onClick={() => navigateToCatalog(subcategory)}
                        >
                          <div className="flex-1 min-w-0">
                            <h5 className={`text-sm font-medium truncate transition-colors duration-200 ${isHighlighted ? 'text-slate-900' : 'text-slate-100'}`}>
                              {subcategory.name}
                            </h5>
                            {subcategory.standard && (
                              <p className={`text-xs truncate transition-colors duration-200 ${isHighlighted ? 'text-slate-800' : 'text-amber-500'}`}>
                                Norma: {subcategory.standard}
                              </p>
                            )}
                            <p className={`text-xs truncate transition-colors duration-200 ${isHighlighted ? 'text-slate-800/80' : 'text-slate-400'}`}>
                              {subcategory.description}
                            </p>
                          </div>
                          <ChevronRight className={`w-4 h-4 ml-2 transition-colors duration-200 ${isHighlighted ? 'text-slate-900' : 'text-slate-400'}`} />
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
                
                {/* Ver Todos los Productos button */}
                <div className="mt-4 flex justify-center">
                  <button
                    onClick={navigateToAllProducts}
                    className="bg-amber-500 hover:bg-amber-600 text-slate-900 font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-300 flex items-center view-all-button"
                  >
                    Ver Todos los Productos
                    <ChevronRight className="ml-2 w-5 h-5" />
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default PPECategoryVisualizer;
