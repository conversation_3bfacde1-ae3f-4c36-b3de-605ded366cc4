import React, { useState } from 'react';
import Slider from 'react-slick';
import { useNavigate } from 'react-router-dom';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const BrandCarousel = () => {
  const navigate = useNavigate();
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());

  const settings = {
    dots: false,
    arrows: false, // Removemos las flechas
    infinite: true,
    speed: 5000,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 0,
    slidesToShow: 8, // Aumentamos el número de slides visibles
    pauseOnHover: true,
    cssEase: "linear",
    responsive: [
      {
        breakpoint: 1536, // 2xl
        settings: {
          slidesToShow: 7,
        }
      },
      {
        breakpoint: 1280, // xl
        settings: {
          slidesToShow: 6,
        }
      },
      {
        breakpoint: 1024, // lg
        settings: {
          slidesToShow: 5,
        }
      },
      {
        breakpoint: 768, // md
        settings: {
          slidesToShow: 4,
        }
      },
      {
        breakpoint: 640, // sm
        settings: {
          slidesToShow: 3,
        }
      }
    ]
  };

  const handleBrandClick = (brandName: string) => {
    navigate(`/catalog?brand=${encodeURIComponent(brandName)}`);
  };

  const handleImageError = (brandName: string) => {
    setFailedImages(prev => new Set(prev).add(brandName));
  };

  const brands = [
    { name: '3M', logo: 'https://www.lubeseguridad.com.ar/scroll/000200122757900010001201913m.png' },
    { name: 'LAKELAND', logo: 'https://www.lakeland.com/wp-content/uploads/elementor/thumbs/lakeland-fire-safety-white-225x57-1-qt736ti477vx1tra2mah3yomp0dijclyphy2wim6ii.webp' },
    { name: 'WYPALL', logo: 'https://d1yjjnpx0p53s8.cloudfront.net/styles/logo-thumbnail/s3/052013/wypall.png?itok=hH7vqNTt' },
    { name: 'LIBUS', logo: 'https://www.lubeseguridad.com.ar/scroll/0002000928860marca-libus.jpg' },
    { name: 'STEELPRO', logo: 'https://www.lubeseguridad.com.ar/scroll/0002000528948marca-steel.jpg' },
    { name: 'DPS', logo: 'https://dpsindustrial.com.ar/--' },
    { name: 'OMBU', logo: 'https://www.lubeseguridad.com.ar/scroll/00020008275380001001120242ombu.png' },
    { name: 'FUNCIONAL', logo: 'https://www.lubeseguridad.com.ar/scroll/00020002275380001001020242funcional.png' },
    { name: 'PAMPERO', logo: 'https://media.licdn.com/dms/image/v2/D4D0BAQFZ_iaLBSRumQ/company-logo_200_200/company-logo_200_200/0/1686924340450/pampero_logo?e=1747872000&v=beta&t=QTG7sfZq7EZE0rWNwD8fku6uJ59ZsjnjiZG05q6q1NY' },
    { name: 'GRAFA70', logo: 'https://grafa70.com.ar/wp-content/uploads/2019/03/cropped-logo_1-2.png' },
    { name: 'MARTOR', logo: 'https://cdn.martor.com/fileadmin/martor.com/theme/dist/img/logo.png?cdnv=1742551700' },
    { name: 'GAMISOL', logo: 'https://www.gamisol.com.ar/wp-content/uploads/2023/12/isologotipoGamisol.svg' },
    { name: 'EAGLE', logo: 'https://eaglesafety.com/---' },
    { name: 'BILVEX', logo: 'https://112cc5d764.cbaul-cdnwnd.com/9f0af1af3b99aa2b896bd83211c7b1c6/system_preview_detail_200000030-886bc89675-public/LOGO%20BIL%20VEX.jpg' },
    { name: 'PRENTEX', logo: 'https://www.cas-seguridad.org.ar/wp-content/uploads/2020/08/logo-prentex.png' },
    { name: 'CONOFLEX', logo: 'https://media.licdn.com/dms/image/v2/C4D0BAQHEIq5y8HxN4g/company-logo_200_200/company-logo_200_200/0/1630575160384/conoflex_argentina_logo?e=1747872000&v=beta&t=w0B2SQYgWxsy0KvsHHPohnA0L7sgWS9GDhIoEkiQPx0' },
    { name: 'MAPA', logo: 'https://www.mapa-pro.com/wp-content/themes/mapapro/images/mapa-professional.png' },
    { name: 'DP', logo: 'https://www.dpsafety.com.ar/wp-content/uploads/2023/04/logo-dp-safety.png' },
    { name: 'UCU', logo: 'https://url.com--' }
  ];

  // Filter out brands with failed images
  const filteredBrands = brands.filter(brand => !failedImages.has(brand.name));

  return (
    <section className="w-full bg-gray-100">
      <div className="py-6">
        {/* Se elimina el título "Marcas que Representamos" */}
        
        <div className="w-full">
          <Slider {...settings}>
            {filteredBrands.map((brand, index) => (
              <div key={index} className="px-2">
                <div 
                  className="h-32 bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center p-4 transform hover:-translate-y-1 cursor-pointer"
                  onClick={() => handleBrandClick(brand.name)}
                >
                  <img 
                    src={brand.logo} 
                    alt={brand.name}
                    className="max-w-[85%] max-h-24 object-contain"
                    onError={() => handleImageError(brand.name)}
                  />
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </section>
  );
};

export default BrandCarousel;
