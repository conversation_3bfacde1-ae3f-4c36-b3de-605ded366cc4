import React, { useState, useMemo, useEffect } from 'react';
import { Search, ChevronDown, X } from 'lucide-react';
import type { CatalogCategoryNode } from '../types/catalog'; // Ruta corregida

interface IndustryFilterProps {
    allIndustries: Array<{ name: string }>; // Mantener si se usa para un filtro de industrias separado
    selectedIndustries: string[];
    onIndustryChange: (industry: string) => void;
    
    brands: string[];
    selectedBrands: string[];
    onBrandChange: (brand: string) => void;
    
    selectedCategories: string[]; // Ahora contendrá IDs de categorías/subcategorías
    onCategoryChange: (categoryId: string) => void; // Ahora maneja IDs

    categoryStructure: CatalogCategoryNode[]; // Añadido para recibir la estructura jerárquica
    className?: string;
    isMobile?: boolean;
    initialOpenSections?: string[];
}

// Props para la sección de categorías jerárquicas
interface HierarchicalCategorySectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  categoryStructure: CatalogCategoryNode[];
  selectedCategories: string[];
  onCategoryChange: (categoryId: string) => void;
}


interface FilterSectionProps {
    title: string;
    isOpen: boolean;
    onToggle: () => void;
    items: string[];
    selectedItems: string[];
    onItemChange: (item: string) => void;
    searchTerm: string;
    maxItems?: number; // Nueva prop para limitar items visibles
}

const FilterSection: React.FC<FilterSectionProps> = ({
    title,
    isOpen,
    onToggle,
    items = [],
    selectedItems = [],
    onItemChange,
    searchTerm = '',
    maxItems // Nueva prop
}) => {
    const filteredItems = useMemo(() => {
        if (!items) return [];
        return items.filter(item =>
            item?.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [items, searchTerm]);

    const itemsToShow = maxItems ? filteredItems.slice(0, maxItems) : filteredItems;
    const hasMoreItems = maxItems && filteredItems.length > maxItems;

    return (
        <div className="py-2 border-b border-gray-100 last:border-0">
            <button
                onClick={onToggle}
                className="w-full flex items-center justify-between text-sm text-gray-700 hover:text-gray-900"
            >
                <div className="flex items-center">
                    <span className="font-medium">{title}</span>
                    {selectedItems.length > 0 && (
                        <span className="ml-2 px-2 py-0.5 text-xs bg-amber-100 text-amber-700 rounded-full">
                            {selectedItems.length}
                        </span>
                    )}
                </div>
                <ChevronDown
                    className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                />
            </button>
            {isOpen && (
                // Aplicar max-h-XX y overflow-y-auto aquí para el scroll de marcas
                <div className={`mt-2 ${maxItems && items.length > maxItems ? 'max-h-40 overflow-y-auto custom-scrollbar' : 'max-h-48 overflow-y-auto custom-scrollbar'}`}>
                    {itemsToShow.length > 0 ? (
                        itemsToShow.map((item) => (
                            <label
                                key={item}
                                className="flex items-center py-1.5 px-2 hover:bg-gray-50 rounded cursor-pointer text-sm"
                            >
                                <input
                                    type="checkbox"
                                    checked={selectedItems.includes(item)}
                                    onChange={() => onItemChange(item)}
                                    className="w-4 h-4 text-amber-600 rounded border-gray-300 focus:ring-amber-500"
                                />
                                <span className="ml-2 text-gray-700">{item}</span>
                            </label>
                        ))
                    ) : (
                        <div className="text-sm text-gray-500 py-2 px-2">
                            No se encontraron resultados
                        </div>
                    )}
                    {/* Si se usa maxItems y hay más, se podría añadir un indicador o botón "Ver más" */}
                </div>
            )}
        </div>
    );
};


// Nuevo componente para la sección de categorías jerárquicas
const HierarchicalCategoryFilterSection: React.FC<HierarchicalCategorySectionProps> = ({
  title,
  isOpen,
  onToggle,
  categoryStructure,
  selectedCategories, // IDs
  onCategoryChange, // (categoryId: string) => void
}) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]); // Almacena IDs de categorías padre expandidas

  const handleParentCategoryToggle = (parentId: string) => {
    setExpandedCategories(prev =>
      prev.includes(parentId) ? prev.filter(id => id !== parentId) : [...prev, parentId]
    );
  };

  const renderCategoryNode = (node: CatalogCategoryNode, level: number = 0) => {
    const isSelected = selectedCategories.includes(node.id);
    const isExpanded = expandedCategories.includes(node.id);

    return (
      <div key={node.id} style={{ marginLeft: `${level * 16}px` }} className="py-1">
        <div className="flex items-center justify-between">
          <label className="flex items-center py-1 px-1 hover:bg-gray-50 rounded cursor-pointer text-sm flex-grow">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onCategoryChange(node.id)}
              className="w-4 h-4 text-amber-600 rounded border-gray-300 focus:ring-amber-500"
            />
            <span className="ml-2 text-gray-700">{node.name}</span>
          </label>
          {node.children && node.children.length > 0 && (
            <button onClick={() => handleParentCategoryToggle(node.id)} className="p-1">
              <ChevronDown
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              />
            </button>
          )}
        </div>
        {isExpanded && node.children && node.children.length > 0 && (
          <div className="mt-1">
            {node.children.map(childNode => renderCategoryNode(childNode, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="py-2 border-b border-gray-100 last:border-0">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between text-sm text-gray-700 hover:text-gray-900"
      >
        <div className="flex items-center">
          <span className="font-medium">{title}</span>
          {/* Contar seleccionados en la jerarquía puede ser más complejo, por ahora lo omitimos o contamos solo los IDs directos */}
          {selectedCategories.length > 0 && (
            <span className="ml-2 px-2 py-0.5 text-xs bg-amber-100 text-amber-700 rounded-full">
              {selectedCategories.length}
            </span>
          )}
        </div>
        <ChevronDown
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      {isOpen && (
        <div className="mt-2 max-h-60 overflow-y-auto custom-scrollbar"> {/* Altura ajustable */}
          {categoryStructure.map(node => renderCategoryNode(node))}
        </div>
      )}
    </div>
  );
};


const IndustryFilter: React.FC<IndustryFilterProps> = ({
    allIndustries = [],
    selectedIndustries = [],
    onIndustryChange,
    brands = [],
    selectedBrands = [],
    onBrandChange,
    categoryStructure, // Usar la prop en lugar del estado local y fetch
    selectedCategories = [],
    onCategoryChange,
    className = '',
    isMobile = false,
    initialOpenSections = ['brands', 'categories'],
}) => {
    const [isIndustryOpen, setIsIndustryOpen] = useState(initialOpenSections.includes('industry'));
    const [isBrandsOpen, setIsBrandsOpen] = useState(initialOpenSections.includes('brands'));
    const [isCategoriesOpen, setIsCategoriesOpen] = useState(initialOpenSections.includes('categories'));

    const clearFilters = () => {
        selectedIndustries.forEach(industry => onIndustryChange(industry));
        selectedBrands.forEach(brand => onBrandChange(brand));
        selectedCategories.forEach(category => onCategoryChange(category));
        // Ya no hay selectedRelevantCategoriesForIndustry para limpiar aquí
    };

    const hasActiveFilters = selectedIndustries.length > 0 ||
                           selectedBrands.length > 0 ||
                           selectedCategories.length > 0; // Solo las categorías generales

    return (
        <div className={`bg-white rounded-xl shadow-lg border border-gray-100 ${
            isMobile ? 'w-full' : 'w-full lg:w-72'
        } ${className}`}>
            <div className="p-4">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Filtros</h2>
                    {hasActiveFilters && (
                        <button
                            onClick={clearFilters}
                            className="text-sm text-amber-600 hover:text-amber-700 flex items-center"
                        >
                            <X className="w-4 h-4 mr-1" />
                            Limpiar
                        </button>
                    )}
                </div>

                {/* Secciones de Filtros - Reordenadas */}
                <div className="space-y-2">
                    {/* 1. Marcas */}
                    <FilterSection
                        title="Marcas"
                        isOpen={isBrandsOpen}
                        onToggle={() => setIsBrandsOpen(!isBrandsOpen)}
                        items={brands}
                        selectedItems={selectedBrands}
                        onItemChange={onBrandChange}
                        searchTerm=""
                        maxItems={5} // Mostrar 5 marcas, con scroll si hay más (manejado por la clase en FilterSection)
                    />

                    {/* 2. Categorías Jerárquicas */}
                    <HierarchicalCategoryFilterSection
                        title="Categorías"
                        isOpen={isCategoriesOpen}
                        onToggle={() => setIsCategoriesOpen(!isCategoriesOpen)}
                        selectedCategories={selectedCategories} // Pasa IDs
                        onCategoryChange={onCategoryChange}    // Maneja IDs
                        categoryStructure={categoryStructure}  // Pasa la estructura de categorías
                    />
                    
                    {/* 3. Industrias (si todavía se usa este filtro separado) */}
                    <FilterSection
                        title="Industrias"
                        isOpen={isIndustryOpen}
                        onToggle={() => setIsIndustryOpen(!isIndustryOpen)}
                        items={allIndustries.map(i => i.name)}
                        selectedItems={selectedIndustries}
                        onItemChange={onIndustryChange}
                        searchTerm=""
                    />

                    {/* La sección 4 de Categorías Relevantes por Industria se elimina */}
                </div>
            </div>
        </div>
    );
};

IndustryFilter.defaultProps = {
    initialOpenSections: ['brands', 'categories'],
};

export default IndustryFilter;
