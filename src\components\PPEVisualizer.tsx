import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { industriesPPE, IndustryPPE, PPEItem, groupPPEByBodyPart, bodyPartNames, BodyPart } from '../config/ppeData';
import { ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';
import { supabase as supabaseClientUntyped } from '../lib/supabase';

const PPEVisualizer: React.FC = () => {
  const [selectedIndustry, setSelectedIndustry] = useState<IndustryPPE>(industriesPPE[0]);
  const [hoveredItem, setHoveredItem] = useState<PPEItem | null>(null);
  const [groupedItems, setGroupedItems] = useState<Record<string, PPEItem[]>>({});
  const navigate = useNavigate();
  const supabase: SupabaseClient<Database> | null = supabaseClientUntyped; // supabaseClientUntyped can be null

  const imageUrls = [
    supabase?.storage // Use optional chaining
      .from('static-images')
      .getPublicUrl(`industry-selector/body-${selectedIndustry.name.toLowerCase()}.jpg`).data?.publicUrl || '/images/placeholder-product.jpg'
  ];
  // const imagesLoaded = useImagePreloader(imageUrls); // Eliminado: La precarga ahora es global

  useEffect(() => {
    setGroupedItems(groupPPEByBodyPart(selectedIndustry.ppeItems)); // imagesLoaded ya no es dependencia
  }, [selectedIndustry]);

  const handleIndustrySelect = (industry: IndustryPPE) => {
    setSelectedIndustry(industry);
    setHoveredItem(null);
  };

  // Función para navegar al catálogo con filtros aplicados
  const navigateToCatalog = (item: PPEItem) => {
    console.log("Navegando al catálogo con:", {
      industry: selectedIndustry.name,
      subfilter: item.name
    });
    
    // Usar el nombre de la industria en mayúsculas como se espera en Catalog.tsx
    const industryParam = encodeURIComponent(selectedIndustry.name);
    
    // Navegar a la página de catálogo con los parámetros
    // Usamos el nombre del item como subfiltro en lugar de categoría
    navigate(`/catalog?industry=${industryParam}&subfilter=${encodeURIComponent(item.name)}`);
  };

  return (
    <div className="w-full bg-slate-900 text-slate-100 min-h-[calc(100vh-80px)] flex flex-col py-8"> {/* Fondo oscuro general y texto claro */}
      <div className="container mx-auto px-28 flex flex-col flex-grow">
        <h2 className="text-4xl font-bold text-slate-100 mb-8 text-center"> {/* Título con texto claro */}
          Equipos de Protección Personal por Industria
        </h2>

        {/* Industry Selector - Tamaño incrementado */}
        <div className="mb-6 overflow-x-auto hide-scrollbar">
          <div className="flex space-x-3 pb-6 justify-center"> {/* Centrado */}
            {industriesPPE.map((industry) => {
              const Icon = industry.icon;
              const isActive = selectedIndustry.id === industry.id;
              
              return (
                <button
                  key={industry.id}
                  onClick={() => handleIndustrySelect(industry)}
                  className={`
                    flex flex-col items-center justify-center p-3 rounded-lg min-w-[100px]
                    transition-all duration-300 ease-in-out
                    ${isActive 
                      ? 'bg-amber-500 text-slate-900 shadow-md scale-105'  // Estilo activo industrial
                      : 'bg-slate-700 text-slate-300 hover:bg-slate-600'} // Estilo inactivo industrial
                  `}
                  aria-pressed={isActive}
                  aria-label={`Seleccionar industria ${industry.displayName}`}
                >
                  <Icon className={`w-6 h-6 mb-2 ${isActive ? 'text-slate-900' : 'text-amber-400'}`} /> {/* Colores de icono industrial */}
                  <span className={`text-sm font-medium ${isActive ? 'text-slate-900' : 'text-slate-300'}`}> {/* Colores de texto industrial */}
                    {industry.displayName}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Contenedor principal - Flex-grow para ocupar espacio disponible */}
        <div className="flex flex-col md:flex-row gap-6 flex-grow"> {/* Reducido gap para mejor ajuste en algunos anchos */}
          {/* Body Visualization - Fondo oscuro */}
          <div className="w-full md:w-1/2 relative bg-slate-800 rounded-xl p-2 flex justify-center items-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedIndustry.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="relative w-full aspect-square flex items-center justify-center" // Aspecto 1:1
              >
                {/* Imagen cargada desde Supabase Storage */}
                {selectedIndustry && (
                  <img
                    src={imageUrls[0]}
                    alt={`Imagen de ${selectedIndustry.displayName}`}
                    className="max-w-full max-h-full object-contain"
                    // onError={(e) => { // Removed onError as the placeholder is already in the imageUrls array
                    //   e.currentTarget.src = '/images/placeholder-product.jpg';
                    // }}
                  />
                )}
                
                {/* PPE Hotspots */}
                {selectedIndustry.ppeItems.map((item) => (
                  <motion.div
                    key={item.id}
                    className="absolute"
                    style={{
                      left: `${item.position.x}%`,
                      top: `${item.position.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: [0.8, 1, 0.8],
                      boxShadow: hoveredItem?.id === item.id // Sombra ámbar
                        ? ['0 0 0 rgba(245, 158, 11, 0.5)', '0 0 20px rgba(245, 158, 11, 0.8)', '0 0 0 rgba(245, 158, 11, 0.5)'] 
                        : ['0 0 0 rgba(245, 158, 11, 0.3)', '0 0 10px rgba(245, 158, 11, 0.3)', '0 0 0 rgba(245, 158, 11, 0.3)']
                    }}
                    transition={{ 
                      repeat: Infinity, 
                      duration: hoveredItem?.id === item.id ? 1.5 : 2.5
                    }}
                    onMouseEnter={() => setHoveredItem(item)}
                    onMouseLeave={() => setHoveredItem(null)}
                    onClick={() => navigateToCatalog(item)}
                  >
                    <div 
                      className={`
                        w-8 h-8 rounded-full flex items-center justify-center
                        ${hoveredItem?.id === item.id ? 'bg-amber-500' : 'bg-slate-600'} // Hotspot con colores industriales
                        cursor-pointer transition-colors duration-300
                      `}
                    >
                      {React.createElement(item.icon, { 
                        className: 'w-4 h-4 text-white',
                        'aria-hidden': true
                      })}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* PPE Details Panel - Ajustado para ocupar espacio disponible */}
          <div className="w-full md:w-1/2 bg-slate-800 rounded-xl p-6 overflow-y-auto shadow-lg flex-grow">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 max-h-96">
              {Object.entries(groupedItems).map(([bodyPart, items]) => (
                <div key={bodyPart} className="bg-slate-700/70 p-4 rounded-lg shadow-md space-y-3">
                  <h4 className="text-sm font-bold text-amber-500 uppercase tracking-wider mb-3">
                    {bodyPartNames[bodyPart as BodyPart] || bodyPart}
                  </h4>
                  <div className="space-y-2.5">
                    {items.map((item) => {
                      const Icon = item.icon;
                      const isHighlighted = hoveredItem?.id === item.id;
                      
                      return (
                        <motion.div
                          key={item.id}
                          className={`
                            p-3 rounded-md flex items-center transition-all duration-200 cursor-pointer
                            ${isHighlighted 
                              ? 'bg-amber-500 shadow-lg scale-102' 
                              : 'bg-slate-700 hover:bg-slate-600'}
                          `}
                          animate={{ 
                            y: isHighlighted ? -1 : 0,
                            // boxShadow: isHighlighted ? '0 4px 8px rgba(0, 0, 0, 0.15)' : '0 1px 2px rgba(0, 0, 0, 0.05)'
                          }}
                          transition={{ type: 'spring', stiffness: 300 }}
                          onMouseEnter={() => setHoveredItem(item)}
                          onMouseLeave={() => setHoveredItem(null)}
                          onClick={() => navigateToCatalog(item)}
                        >
                          <div className={`w-8 h-8 flex items-center justify-center rounded-full mr-3 transition-colors duration-200
                            ${isHighlighted ? 'bg-amber-600' : 'bg-slate-600'}
                          `}>
                            <Icon className={`w-4 h-4 transition-colors duration-200 ${isHighlighted ? 'text-white' : 'text-amber-400'}`} aria-hidden="true" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h5 className={`text-sm font-medium truncate transition-colors duration-200 ${isHighlighted ? 'text-slate-900' : 'text-slate-100'}`}>{item.name}</h5>
                            {item.standard && (
                              <p className={`text-xs truncate transition-colors duration-200 ${isHighlighted ? 'text-slate-800' : 'text-amber-500'}`}>Norma: {item.standard}</p>
                            )}
                          </div>
                          <ChevronRight className={`w-4 h-4 ml-2 transition-colors duration-200 ${isHighlighted ? 'text-slate-900' : 'text-slate-400'}`} />
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PPEVisualizer;
