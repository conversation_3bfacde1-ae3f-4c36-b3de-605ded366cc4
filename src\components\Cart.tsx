import React, { useEffect, useRef, useCallback, useState } from 'react';
import { ShoppingCart, X, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import QuoteRequestModal from './QuoteRequestModal';
import { useAuth } from '../context/AuthContext';
import { FEATURE_FLAGS } from '../config/featureFlags';

interface CartItem {
    _id: string;
    name: string;
    price: string;
    quantity: number;
    category: string;
    brand: string;
    image_url?: string;
}

interface CartProps {
    isOpen: boolean;
    onClose: () => void;
    items: CartItem[];
    onUpdateQuantity: (id: string, quantity: number) => void;
    onRemoveItem: (id: string) => void;
}

const Cart: React.FC<CartProps> = ({ 
    isOpen, 
    onClose, 
    items, 
    onUpdateQuantity, 
    onRemoveItem
}) => {
    const navigate = useNavigate();
    const { isAuthenticated } = useAuth();
    const cartRef = useRef<HTMLDivElement>(null);
    const [showQuoteModal, setShowQuoteModal] = useState(false);

    const handleClickOutside = useCallback((event: MouseEvent) => {
        // Solo cerrar si el click fue fuera del cart Y fuera del modal
        const modalElement = document.querySelector('.quote-modal-container');
        if (cartRef.current && 
            !cartRef.current.contains(event.target as Node) && 
            !modalElement?.contains(event.target as Node) &&
            isOpen) {
            onClose();
        }
    }, [isOpen, onClose]);

    useEffect(() => {
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [isOpen, handleClickOutside]);

    const handleQuoteRequest = (data: { fullName: string; company: string; email: string }) => {
        console.log('Quote request data:', data);
        console.log('Cart items:', items);
        // Limpiar el carrito después de enviar la cotización
        items.forEach(item => onRemoveItem(item._id));
        setShowQuoteModal(false);
        onClose();
    };

    // Calcular el total fuera del JSX
    const total = items.reduce((sum, item) => {
        return sum + (Number(item.price) || 0) * item.quantity;
    }, 0);

    // Verificar si se puede mostrar el carrito
    if (!isOpen || (!FEATURE_FLAGS.ALLOW_GUEST_CART && !isAuthenticated)) return null;

    const handleViewOrder = () => {
        onClose(); // Cerrar el carrito
        navigate('/order-detail'); // Navegar a la página de detalle
    };

    return (
        <>
            <div 
                ref={cartRef}
                className={`fixed inset-y-0 right-0 max-w-sm w-full bg-white shadow-xl 
                           transform ${isOpen ? 'translate-x-0' : 'translate-x-full'} 
                           transition-transform duration-300 ease-in-out z-50
                           mt-[var(--navbar-height)]`} // Añadido margen superior
                style={{ height: 'calc(100vh - var(--navbar-height))' }} // Ajustar altura
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex flex-col h-full"> {/* Contenedor flex para organizar el contenido */}
                    <div className="p-3 border-b flex justify-between items-center">
                        <div className="flex items-center">
                            <ShoppingCart className="w-5 h-5 text-blue-600 mr-2" />
                            <h2 className="text-lg font-semibold">Solicitud de Cotización</h2>
                        </div>
                        <button
                            onClick={onClose}
                            className="text-gray-500 hover:text-gray-700"
                        >
                            <X className="w-5 h-5" />
                        </button>
                    </div>

                    <div className="flex-1 overflow-y-auto p-4">
                        {items.map((item) => (
                            <div key={item._id} className="flex items-start space-x-3 bg-gray-50 p-2 rounded-lg mb-2">
                                {/* Imagen del producto */}
                                <div className="w-12 h-12 flex-shrink-0">
                                    <img
                                        src={item.image_url || '/images/placeholder-product.jpg'}
                                        alt={item.name}
                                        className="w-full h-full object-contain rounded-md"
                                        onError={(e) => {
                                            (e.target as HTMLImageElement).src = '/images/placeholder-product.jpg';
                                        }}
                                    />
                                </div>

                                {/* Información del producto */}
                                <div className="flex-1 min-w-0">
                                    <div className="line-clamp-2 text-xs">
                                        <span className="font-semibold text-gray-900">{item.name}</span>
                                        <span className="font-bold text-gray-700"> | {item.brand}</span>
                                    </div>
                                    <div className="text-xs text-gray-600 mt-0.5">
                                        <span>{item.category}</span>
                                    </div>
                                </div>

                                {/* Precio y controles de cantidad */}
                                <div className="flex flex-col items-end space-y-1 ml-2">
                                    {FEATURE_FLAGS.SHOW_CART_PRICING && isAuthenticated && (
                                        <p className="text-xs text-blue-600 font-semibold">
                                            ${Number(item.price).toFixed(2)}
                                        </p>
                                    )}
                                    <div className="flex items-center space-x-1">
                                        <button
                                            onClick={() => onUpdateQuantity(item._id, Math.max(0, item.quantity - 1))}
                                            className="w-6 h-6 flex items-center justify-center rounded-full border border-gray-300 hover:bg-gray-100 text-xs"
                                        >
                                            -
                                        </button>
                                        <span className="w-6 text-center text-xs">{item.quantity}</span>
                                        <button
                                            onClick={() => onUpdateQuantity(item._id, item.quantity + 1)}
                                            className="w-6 h-6 flex items-center justify-center rounded-full border border-gray-300 hover:bg-gray-100 text-xs"
                                        >
                                            +
                                        </button>
                                        <button
                                            onClick={() => onRemoveItem(item._id)}
                                            className="w-6 h-6 flex items-center justify-center text-red-500 hover:text-red-600"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="border-t p-4 mt-auto">
                        {(isAuthenticated || FEATURE_FLAGS.ALLOW_GUEST_CART) && (
                            <>
                                {FEATURE_FLAGS.SHOW_CART_PRICING && isAuthenticated && (
                                    <div className="flex justify-between mb-4">
                                        <span className="font-medium">Total:</span>
                                        <span className="font-bold">${total.toFixed(2)}</span>
                                    </div>
                                )}
                                <div className="flex gap-2">
                                    {/* Botón Ver Detalle (izquierda) */}
                                    <button
                                        onClick={handleViewOrder}
                                        className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                        disabled={items.length === 0}
                                    >
                                        Ver Detalle
                                    </button>

                                    {/* Botón Emitir Orden/Solicitar Cotización (derecha) */}
                                    <button
                                        onClick={() => {
                                            if (isAuthenticated) {
                                                // Lógica para emitir orden
                                                handleQuoteRequest({
                                                    fullName: '', // Obtener del perfil del usuario
                                                    company: '',  // Obtener del perfil del usuario
                                                    email: ''     // Obtener del perfil del usuario
                                                });
                                            } else if (FEATURE_FLAGS.ALLOW_GUEST_QUOTE_REQUEST) {
                                                // Mostrar modal para usuarios no autenticados
                                                setShowQuoteModal(true);
                                            } else {
                                                // Redirigir al login si no se permiten cotizaciones de invitados
                                                navigate('/login');
                                            }
                                        }}
                                        className="flex-1 border-2 border-blue-600 text-blue-600 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                                        disabled={items.length === 0}
                                    >
                                        {isAuthenticated ? 'Emitir Orden' : 'Solicitar Cotización'}
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
            {/* Modal de cotización para usuarios no autenticados */}
            {FEATURE_FLAGS.ALLOW_GUEST_QUOTE_REQUEST && (
                <QuoteRequestModal
                    isOpen={showQuoteModal}
                    onClose={() => setShowQuoteModal(false)}
                    onSubmit={handleQuoteRequest}
                />
            )}
        </>
    );
};

export default Cart;
