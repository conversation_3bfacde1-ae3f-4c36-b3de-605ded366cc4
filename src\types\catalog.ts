/**
 * Representa un producto en el catálogo, adaptado de products.json.
 */
export interface CatalogProduct {
  id: string;
  name: string;
  description: string;
  price: string; // Ajustado a string para compatibilidad con ProductCard, antes number
  image_url: string; // Para Supabase y consistencia con el tipo Product original
  imageUrl: string; // Hecho requerido para compatibilidad con ProductCard
  
  brand: string; // Hecho requerido para compatibilidad con ProductCard
  stock: number; // Hecho requerido para compatibilidad
  icon?: string; // Campo de Supabase
  industries?: string[]; // Campo de Supabase
  categoryIds: string[]; // Nuevos IDs de categoría de catalogStructure

  // Campos para compatibilidad con el tipo Product original y ProductCard
  category: string; // Categoría original o principal para visualización simple
  code: string; // Hecho requerido para compatibilidad
  normative: string; // Hecho requerido para compatibilidad
  
  // technicalSpecs sigue siendo el objeto estructurado, ahora requerido
  technicalSpecs: {
    caracteristicas?: string;
    especificaciones?: string;
    presentacion?: string;
    // Podrías añadir más campos genéricos si es necesario: [key: string]: string;
  };
  // Campos directos para compatibilidad, ahora requeridos
  características: string;
  especificaciones: string;
  presentación: string;
  documentacion: string; // Hecho requerido

  featured_status?: {
    is_featured: boolean;
    featured_type?: 'featured' | 'new' | 'top';
    end_date?: string;
  };

  // Para migración desde products.json o sistemas antiguos
  originalCategory_from_json?: string;
}

/**
 * Representa un nodo en el árbol de categorías del catálogo.
 * Un nodo puede ser una categoría principal o una subcategoría.
 */
export interface CatalogCategoryNode {
  id: string; // Identificador único para la categoría (ej. 'proteccion-personal', 'respiratoria')
  name: string; // Nombre para mostrar de la categoría (ej. 'Protección Personal', 'Respiratoria')
  children?: CatalogCategoryNode[]; // Subcategorías
  // Podrías añadir productIds aquí si decides que las categorías almacenen referencias a productos.
  // productIds?: string[];
}