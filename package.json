{"name": "CR-Work", "private": true, "version": "0.8.2", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "node server.js", "postinstall": "npm run build", "dev:server": "nodemon server.js", "verify-images": "node scripts/validateImages.js", "predeploy": "npm run version:patch && npm run verify-images", "migrate-images": "node scripts/migrateImages.js", "update-image-urls": "node scripts/updateImageUrls.js", "version:major": "node scripts/version-bump.js major", "version:minor": "node scripts/version-bump.js minor", "version:patch": "node scripts/version-bump.js patch"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.0.0", "@vercel/node": "^5.2.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^12.7.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-router-dom": "^7.2.0", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "supabase": "^2.20.12", "tailwind-merge": "^2.6.0", "winston": "^3.10.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20230914.0", "@rollup/plugin-inject": "^5.0.0", "@types/node": "^20.19.0", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.4", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "source-map-js": "^1.0.2", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vite": "^5.4.18", "wrangler": "^3.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}