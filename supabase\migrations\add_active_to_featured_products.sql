-- Agregar columna active a featured_products
ALTER TABLE public.featured_products
ADD COLUMN active boolean DEFAULT true;

-- Actualizar la función get_active_featured_products
CREATE OR REPLACE FUNCTION public.get_active_featured_products(
    feature_type text default null,
    limit_count integer default 4
)
RETURNS TABLE (
    id uuid,
    product_id uuid,
    featured_type text,
    product_data jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fp.id,
        fp.product_id,
        fp.featured_type,
        to_jsonb(p.*) as product_data
    FROM featured_products fp
    JOIN products p ON p.id = fp.product_id
    WHERE 
        fp.active = true
        AND (feature_type IS NULL OR fp.featured_type = feature_type)
        AND (fp.end_date IS NULL OR fp.end_date > now())
        AND fp.start_date <= now()
    ORDER BY fp.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;