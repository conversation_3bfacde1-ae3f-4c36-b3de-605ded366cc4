/* Asegura que el área interactiva sea continua */
.group:hover .group-hover\:block {
    display: block;
}

/* Crea un área invisible que conecta el botón con el menú */
.group::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Área extra para asegurar una transición suave */
    background: transparent;
}

/* Asegura que el menú desplegable tenga un z-index apropiado */
.group {
    position: relative;
    z-index: 50;
}

/* Estilo para el menú desplegable */
.group > div[class*="absolute"] {
    z-index: 51;
}

.dropdown-overlay {
    position: fixed;
    inset: 0;
    background: transparent;
    z-index: 40;
}

/* Mejoras visuales para el menú móvil */
.mobile-menu-transition {
    transition: all 0.3s ease-out;
}

.mobile-menu-item {
    transition: background-color 0.2s ease;
}

.mobile-menu-item:hover {
    background-color: rgba(251, 191, 36, 0.1);
}

/* Asegura que los dropdowns estén por encima del contenido pero debajo del navbar */
.dropdown-menu {
    z-index: 45;
}

/* Estilos base del navbar con gradiente y blur */
.navbar-container {
  @apply fixed w-full top-0 left-0 bg-gradient-to-b from-black via-black/90 to-black/80 
         shadow-lg border-b border-amber-300/10;
  backdrop-filter: blur(10px);
  z-index: var(--z-navbar);
  height: 48px; /* Altura reducida para móvil */
}

/* Badge del carrito con z-index máximo */
.cart-badge {
  @apply absolute bg-red-500 text-white text-xs font-bold
         rounded-full flex items-center justify-center;
  min-width: 20px;
  height: 20px;
  padding: 2px 6px;
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);
  z-index: 9999;
  top: -8px;
  right: -10px;
}

/* Ajustes responsive */
@media (min-width: 768px) {
  .navbar-container {
    height: 52px; /* Altura reducida para desktop */
  }
}

/* Contenedor principal del navbar */
.navbar-container nav {
  height: 100%;
}

.navbar-container nav > div {
  height: 100%;
}

.navbar-container nav > div > div {
  height: 100%;
}

/* Asegurar que los dropdowns del navbar también estén por encima */
.dropdown-menu {
  z-index: calc(var(--z-navbar) + 1);
}

/* Ajuste para el menú móvil */
.mobile-menu {
  z-index: calc(var(--z-navbar) + 2);
}

/* Links de navegación con animaciones y líneas indicadoras */
.nav-link {
  @apply relative text-gray-300 hover:text-amber-300 
         transition-all duration-300;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-1/2 w-0 h-0.5 bg-amber-300 
         transform -translate-x-1/2 transition-all duration-300 ease-out;
}

.nav-link:hover::after {
  @apply w-full;
}

/* Dropdown mejorado con efectos visuales */
.dropdown-menu {
  @apply absolute left-0 top-full mt-1 w-64 
         bg-gradient-to-b from-black/95 to-black/90
         border border-amber-300/20 rounded-lg 
         shadow-lg shadow-amber-900/20
         transform opacity-0 -translate-y-2 scale-95
         transition-all duration-300 ease-out pointer-events-none;
  backdrop-filter: blur(12px);
}

.group:hover .dropdown-menu {
  @apply opacity-100 translate-y-0 scale-100 pointer-events-auto;
}

/* Elementos del dropdown con hover mejorado */
.dropdown-item {
  @apply block w-full text-left px-4 py-3 text-sm text-gray-300 
         transition-all duration-200 first:rounded-t-lg last:rounded-b-lg;
  background: linear-gradient(to right, transparent 50%, rgba(251, 191, 36, 0.1) 50%);
  background-size: 200% 100%;
  background-position: 0% 0%;
}

.dropdown-item:hover {
  @apply text-amber-300;
  background-position: -100% 0%;
}

/* Botones de acción con efectos mejorados */
.action-button {
  @apply relative overflow-hidden rounded-lg transition-all duration-300;
  background: linear-gradient(45deg, rgba(251, 191, 36, 1) 0%, rgba(245, 158, 11, 1) 100%);
  box-shadow: 0 2px 10px rgba(251, 191, 36, 0.2);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.action-button:active {
  transform: translateY(0);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

/* Animación del icono de navegación */
.nav-icon {
  @apply transition-transform duration-300 ease-out;
}

.group:hover .nav-icon {
  @apply rotate-180;
}

/* Badge del carrito animado */
.cart-button {
  position: relative;
}

.cart-badge {
  @apply absolute bg-red-500 text-white text-xs font-bold
         rounded-full flex items-center justify-center;
  min-width: 20px;  /* Aumentado el tamaño mínimo */
  height: 20px;     /* Altura fija */
  padding: 2px 6px; /* Más padding horizontal */
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);
  z-index: calc(var(--z-navbar) + 10);
  top: -8px;        /* Ajustada la posición */
  right: -10px;     /* Ajustada la posición */
}

.cart-button:hover .cart-badge {
  @apply scale-110;
  box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
}

/* Transición móvil-desktop */
@media (max-width: 768px) {
  .navbar-container {
    @apply transition-all duration-300;
    height: 60px;
  }
}

@media (min-width: 769px) {
  .navbar-container {
    @apply transition-all duration-300;
    height: 72px;
  }
}

/* Animación para el menú móvil */
.mobile-menu {
  @apply transform transition-transform duration-300 ease-out;
}

.mobile-menu[data-state="open"] {
  @apply translate-x-0;
}

.mobile-menu[data-state="closed"] {
  @apply translate-x-full;
}

/* Menú móvil */
.mobile-menu {
  @apply fixed inset-0 z-[1001] md:hidden;
  backdrop-filter: blur(8px);
}

.mobile-menu-panel {
  @apply absolute right-0 top-0 h-full w-72 bg-black/95 transform transition-transform 
         duration-300 ease-out border-l border-amber-300/20;
}

.mobile-menu-item {
  @apply flex items-center space-x-3 text-lg text-amber-300 hover:text-amber-400 
         py-4 px-6 border-b border-amber-300/20 transition-colors duration-200;
}

/* Iconos en la navegación */
.nav-icon {
  @apply w-5 h-5 transition-transform duration-200 ease-out;
}

.group:hover .nav-icon {
  @apply transform rotate-180;
}

/* Cart badge animation */
.cart-badge {
  @apply absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 
         rounded-full flex items-center justify-center transform transition-transform duration-200;
}

.cart-button:hover .cart-badge {
  @apply scale-110;
}

/* Search bar animation */
.search-container {
  @apply absolute left-0 right-0 transform transition-all duration-300;
}

.search-input {
  @apply w-full pl-10 pr-4 py-3 bg-black/80 border border-amber-300/30 
         text-white placeholder-gray-400 rounded-lg focus:ring-2 
         focus:ring-amber-500 focus:border-transparent backdrop-blur-sm;
}

/* Ajuste específico para el carrito en móvil */
@media (max-width: 768px) {
  .cart-badge {
    min-width: 18px;    /* Ligeramente más pequeño en móvil */
    height: 18px;
    font-size: 0.7rem;
    top: -6px;          /* Ajustada la posición para móvil */
    right: -8px;
  }
}

/* Ajuste para el botón del carrito */
.cart-button {
  @apply relative inline-flex items-center justify-center;
  padding-right: 12px; /* Más espacio para el badge */
}

/* Hover effect */
.cart-button:hover .cart-badge {
  @apply scale-110;
  box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
}

/* Estilo para las estrellas del campeonato */
.navbar-container .stars-container {
  filter: grayscale(100%);
  letter-spacing: -1px;
  opacity: 0.8;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Tamaño de las estrellas individuales - incrementado 10% */
.stars-size {
  font-size: 1.1rem;  /* 18px * 1.1 = 19.8px (móvil) */
}

@media (min-width: 768px) {
  .navbar-container .stars-container {
    left: -124px; /* Un poco más separado en desktop */
  }

  .stars-size {
    font-size: 1.375rem; /* 20px * 1.1 = 22px (desktop) */
  }
}













