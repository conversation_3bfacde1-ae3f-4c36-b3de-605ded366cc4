import { 
  HardHat, Shield, Glasses, Headphones, Shirt, Footprints, 
  Wrench, Construction, Factory, Beaker, 
  Warehouse, Stethoscope, Tractor, Utensils, Hammer, Droplets,
  Wind
} from 'lucide-react';

// Tipos para las partes del cuerpo
export type BodyPart = 'head' | 'face' | 'ears' | 'torso' | 'arms' | 'hands' | 'legs' | 'feet' | 'respiratory' | 'full-body';

// Interfaz para elementos de EPP
export interface PPEItem {
  id: string;
  name: string;
  description: string;
  icon: any;
  standard?: string;
  bodyPart: BodyPart;
  position: { x: number; y: number };
}

// Interfaz para industrias
export interface IndustryPPE {
  id: string;
  name: string;
  displayName: string;
  icon: any;
  bodyImage: string;
  description: string;
  ppeItems: PPEItem[];
}

// Sección de Industrias con EPP
export const industriesPPE: IndustryPPE[] = [
  {
    id: 'construccion',
    name: 'CONSTRUCCION',
    displayName: 'Construcción',
    icon: Construction,
    bodyImage: '/images/body-construction.jpg',
    description: 'Equipamiento de protección para trabajadores de la construcción',
    ppeItems: [
      {
        id: 'casco',
        name: 'Casco de Seguridad',
        description: 'Protección contra impactos y caída de objetos',
        icon: HardHat,
        standard: 'ANSI Z89.1',
        bodyPart: 'head',
        position: { x: 50, y: 10 }
      },
      {
        id: 'lentes',
        name: 'Lentes de Seguridad',
        description: 'Protección contra partículas y salpicaduras',
        icon: Glasses,
        standard: 'ANSI Z87.1',
        bodyPart: 'face',
        position: { x: 50, y: 15 }
      },
      {
        id: 'chaleco',
        name: 'Chaleco Reflectante',
        description: 'Alta visibilidad en zonas de trabajo',
        icon: Shirt,
        standard: 'ANSI/ISEA 107',
        bodyPart: 'torso',
        position: { x: 50, y: 35 }
      },
      {
        id: 'guantes',
        name: 'Guantes de Seguridad',
        description: 'Protección contra cortes y abrasiones',
        icon: Wrench,
        standard: 'EN 388',
        bodyPart: 'hands',
        position: { x: 30, y: 45 }
      },
      {
        id: 'botas',
        name: 'Calzado de Seguridad',
        description: 'Protección contra impactos y perforaciones',
        icon: Footprints,
        standard: 'ASTM F2413',
        bodyPart: 'feet',
        position: { x: 50, y: 85 }
      }
    ]
  },
  {
    id: 'mineria',
    name: 'MINERIA',
    displayName: 'Minería',
    icon: Wrench,
    bodyImage: '/images/body-minera.jpg',
    description: 'Equipamiento de protección para trabajadores de la minería',
    ppeItems: [
      {
        id: 'casco-minero',
        name: 'Casco con Lámpara',
        description: 'Protección contra impactos y visibilidad en túneles',
        icon: HardHat,
        standard: 'ANSI Z89.1',
        bodyPart: 'head',
        position: { x: 50, y: 10 }
      },
      {
        id: 'respirador',
        name: 'Respirador',
        description: 'Protección contra polvo y partículas',
        icon: Wind,
        standard: 'NIOSH N95',
        bodyPart: 'respiratory',
        position: { x: 50, y: 30 }
      },
      {
        id: 'botas-mineras',
        name: 'Botas de Seguridad',
        description: 'Protección contra impactos y humedad',
        icon: Footprints,
        standard: 'ASTM F2413',
        bodyPart: 'feet',
        position: { x: 50, y: 85 }
      }
    ]
  },
  {
    id: 'manufactura',
    name: 'MANUFACTURA',
    displayName: 'Manufactura',
    icon: Factory,
    bodyImage: '/images/body-manufacturing.png',
    description: 'Equipamiento de protección para trabajadores de manufactura',
    ppeItems: [
      {
        id: 'proteccion-auditiva',
        name: 'Protección Auditiva',
        description: 'Protección contra ruido industrial',
        icon: Headphones,
        standard: 'ANSI S3.19',
        bodyPart: 'ears',
        position: { x: 60, y: 15 }
      },
      {
        id: 'guantes-industriales',
        name: 'Guantes Industriales',
        description: 'Protección contra cortes y abrasiones',
        icon: Wrench,
        standard: 'EN 388',
        bodyPart: 'hands',
        position: { x: 30, y: 45 }
      }
    ]
  },
  {
    id: 'petrolera',
    name: 'PETROLERA',
    displayName: 'Petrolera',
    icon: Droplets,
    bodyImage: '/images/body-petrolera.jpg',
    description: 'Equipamiento de protección para trabajadores de la industria petrolera',
    ppeItems: [
      {
        id: 'traje-ignifugo',
        name: 'Traje Ignífugo',
        description: 'Protección contra fuego y altas temperaturas',
        icon: Shield,
        standard: 'NFPA 2112',
        bodyPart: 'full-body',
        position: { x: 50, y: 50 }
      }
    ]
  },
  {
    id: 'quimica',
    name: 'QUIMICA',
    displayName: 'Química',
    icon: Beaker,
    bodyImage: '/images/body-chemical.jpg',
    description: 'Equipamiento de protección para trabajadores de la industria química',
    ppeItems: [
      {
        id: 'mascara-gases',
        name: 'Máscara de Gases',
        description: 'Protección contra gases y vapores tóxicos',
        icon: Wind,
        standard: 'EN 14387',
        bodyPart: 'respiratory',
        position: { x: 50, y: 20 }
      }
    ]
  },
  {
    id: 'metalmecanica',
    name: 'METALMECANICA',
    displayName: 'Metalmecánica',
    icon: Hammer,
    bodyImage: '/images/body-metalmechanics.jpg',
    description: 'Equipamiento de protección para trabajadores metalmecánicos',
    ppeItems: [
      {
        id: 'careta-soldador',
        name: 'Careta de Soldador',
        description: 'Protección contra radiación y chispas',
        icon: Shield,
        standard: 'ANSI Z87.1',
        bodyPart: 'face',
        position: { x: 50, y: 15 }
      }
    ]
  },
  {
    id: 'logistica',
    name: 'LOGISTICA',
    displayName: 'Logística',
    icon: Warehouse,
    bodyImage: '/images/body-logistics.png',
    description: 'Equipamiento de protección para trabajadores de logística y almacenes',
    ppeItems: [
      {
        id: 'faja-lumbar',
        name: 'Faja Lumbar',
        description: 'Soporte para levantamiento de cargas',
        icon: Shirt,
        standard: 'NIOSH',
        bodyPart: 'torso',
        position: { x: 50, y: 40 }
      }
    ]
  },
  {
    id: 'salud',
    name: 'SALUD',
    displayName: 'Salud',
    icon: Stethoscope,
    bodyImage: '/images/body-healthcare.png',
    description: 'Equipamiento de protección para profesionales de la salud',
    ppeItems: [
      {
        id: 'mascarilla-n95',
        name: 'Mascarilla N95',
        description: 'Protección contra patógenos aéreos',
        icon: Wind,
        standard: 'NIOSH N95',
        bodyPart: 'respiratory',
        position: { x: 50, y: 20 }
      }
    ]
  },
  {
    id: 'agricola',
    name: 'AGRICOLA',
    displayName: 'Agrícola',
    icon: Tractor,
    bodyImage: '/images/body-agriculture.png',
    description: 'Equipamiento de protección para trabajadores agrícolas',
    ppeItems: [
      {
        id: 'sombrero-protector',
        name: 'Sombrero Protector',
        description: 'Protección contra radiación solar',
        icon: HardHat,
        bodyPart: 'head',
        position: { x: 50, y: 10 }
      }
    ]
  },
  {
    id: 'alimenticia',
    name: 'ALIMENTICIA',
    displayName: 'Alimenticia',
    icon: Utensils,
    bodyImage: '/images/body-food.png',
    description: 'Equipamiento de protección para trabajadores de la industria alimenticia',
    ppeItems: [
      {
        id: 'cofia',
        name: 'Cofia',
        description: 'Prevención de contaminación de alimentos',
        icon: HardHat,
        standard: 'FDA',
        bodyPart: 'head',
        position: { x: 50, y: 10 }
      }
    ]
  }
];

// Función para agrupar EPP por parte del cuerpo
export const groupPPEByBodyPart = (ppeItems: PPEItem[]) => {
  const groups: Record<string, PPEItem[]> = {};
  
  ppeItems.forEach(item => {
    if (!groups[item.bodyPart]) {
      groups[item.bodyPart] = [];
    }
    groups[item.bodyPart].push(item);
  });
  
  return groups;
};

// Mapeo de nombres de partes del cuerpo para mostrar
export const bodyPartNames: Record<BodyPart, string> = {
  'head': 'Cabeza',
  'face': 'Cara',
  'ears': 'Oídos',
  'torso': 'Torso',
  'arms': 'Brazos',
  'hands': 'Manos',
  'legs': 'Piernas',
  'feet': 'Pies',
  'respiratory': 'Sistema Respiratorio',
  'full-body': 'Cuerpo Completo'
};
