import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';

const API_URL = process.env.VITE_API_URL || 'https://cr-work.up.railway.app';

async function validateImages() {
    const imagesToCheck = ['epp.jpg', 'promoslides.jpg'];
    
    for (const image of imagesToCheck) {
        try {
            const response = await fetch(`${API_URL}/images/${image}`);
            if (!response.ok) {
                console.error(`❌ Image ${image} not accessible at ${API_URL}/images/${image}`);
                console.error(`Status: ${response.status}`);
            } else {
                console.log(`✅ Image ${image} accessible`);
            }
        } catch (error) {
            console.error(`❌ Error checking ${image}:`, error.message);
        }
    }
}

validateImages();