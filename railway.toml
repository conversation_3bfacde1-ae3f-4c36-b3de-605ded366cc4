[build]
builder = "nixpacks"
buildCommand = "npm run build"

[deploy]
startCommand = "npm start"
healthcheckPath = "/health"
healthcheckTimeout = 100
restartPolicyType = "on_failure"

[env]
NODE_ENV = "production"
NPM_CONFIG_PRODUCTION = "true"

[[services]]
name = "cr-work-api"
envs = [
  "VITE_SUPABASE_URL",
  "VITE_SUPABASE_ANON_KEY"
]

[[services.ports]]
port = 3010
internal = false

[environments]
  [environments.production]
    envs = { NODE_ENV = "production" }
  [environments.staging]
    envs = { NODE_ENV = "production" }
  [environments.development]
    envs = { NODE_ENV = "development" }
