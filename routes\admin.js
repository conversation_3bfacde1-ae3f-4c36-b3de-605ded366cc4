import express from 'express';
import { checkRole } from '../middleware/roleAuth.js';

const router = express.Router();

// Ruta accesible solo para administradores
router.get('/dashboard', 
  checkRole(['admin']), 
  async (req, res) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*');

      if (error) throw error;
      res.json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Ruta accesible para clientes medianos y grandes
router.get('/reports', 
  checkRole(['medium', 'large', 'admin']), 
  async (req, res) => {
    // Implementación...
});

export default router;