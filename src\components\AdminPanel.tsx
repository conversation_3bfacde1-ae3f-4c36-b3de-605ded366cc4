import React from 'react';
import { useAuth } from '../context/AuthContext';
import { Navigate } from 'react-router-dom';

const AdminPanel: React.FC = () => {
  const { userProfile, hasRole } = useAuth();

  // Agregar logging para debug
  console.log('AdminPanel - Current user profile:', userProfile);

  if (!userProfile || !hasRole(['admin'])) {
    console.log('Access denied to AdminPanel');
    return <Navigate to="/unauthorized" replace />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Panel de Administración</h1>
      {/* Contenido del panel de administración */}
    </div>
  );
};

export default AdminPanel;
