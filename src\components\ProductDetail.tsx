import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import WhatsAppButton from './WhatsAppButton';
import { Download } from 'lucide-react';
import { Product } from '../types';
import { IMAGES } from '../config/constants';
import { useAuth } from '../context/AuthContext';
import { FEATURE_FLAGS } from '../config/featureFlags';

// Añade este console.log temporal para debug
console.log('Feature Flags en ProductDetail:', FEATURE_FLAGS);

interface ProductDetailProps {
  product: Product;
  onAddToCart: (product: Product, quantity: number) => void;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ product, onAddToCart }) => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('características');
  const [imgError, setImgError] = useState(false);

  const handleImageError = () => setImgError(true);
  const imageSource = imgError || !product.image_url ? IMAGES.DEFAULT_PRODUCT : product.image_url;

  const tabs = [
    { id: 'características', label: 'Características' },
    { id: 'especificaciones', label: 'Especificaciones' },
    { id: 'presentación', label: 'Presentación' },
    { id: 'documentación', label: 'Documentación' },
  ];

  const renderPrice = () => {
    // Mostrar precio real si:
    // 1. Se permiten mostrar precios (SHOW_CART_PRICING = true) Y
    // 2. (No se requiere autenticación para precios O el usuario está autenticado)
    if (FEATURE_FLAGS.SHOW_CART_PRICING && (!FEATURE_FLAGS.REQUIRE_AUTH_FOR_PRICES || isAuthenticated)) {
      return `$${Number(product.price).toFixed(2)}`;
    }
    return '$-';
  };

  return (
    <div 
      className="min-h-screen bg-gray-50"
      style={{
        paddingTop: 'var(--navbar-height)',
      }}
    >
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb actualizado */}
        <nav className="mb-8 text-sm bg-white p-3 rounded-lg shadow-sm border border-gray-100">
          <ol className="flex items-center space-x-2">
            <li>
              <Link to="/" className="text-primary hover:text-primary-dark font-medium">
                Inicio
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li>
              <Link to="/catalog" className="text-primary hover:text-primary-dark font-medium">
                Productos
              </Link>
            </li>
            <li className="text-gray-400">/</li>
            <li className="text-secondary font-medium">{product.name}</li>
          </ol>
        </nav>

        {/* Product Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Image Section - Fondo blanco para mejor contraste */}
          <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
            <div className="aspect-square relative overflow-hidden rounded-lg bg-gray-50 p-4">
              <img
                src={imageSource}
                alt={product.name}
                onError={handleImageError}
                className="w-full h-full object-contain"
              />
            </div>
          </div>

          {/* Product Info Section */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
              <h1 className="text-3xl font-bold text-secondary mb-3">{product.name}</h1>
              <div className="flex items-center gap-3 text-sm text-gray-600 mb-4">
                <span className="font-medium bg-gray-50 px-2 py-1 rounded">{product.brand}</span>
                <span>•</span>
                <span className="bg-gray-50 px-2 py-1 rounded">Código: {product.code || product.id}</span>
              </div>
              <p className="text-secondary-light text-lg mb-6 leading-relaxed">{product.description}</p>
              
              {/* Sección de precios y acciones */}
              <div className="space-y-4">
                <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                  <span className="text-2xl font-bold text-primary">
                    {renderPrice()}
                  </span>
                </div>
                {(FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated) ? (
                  <div className="flex items-center gap-4">
                    <div className="w-32">
                      <input
                        type="number"
                        min="1"
                        value={quantity}
                        onChange={(e) => setQuantity(Number(e.target.value))}
                        className="w-full px-4 py-3 bg-gray-50 border-2 border-gray-200 rounded-lg"
                      />
                    </div>
                    <button
                      onClick={() => {
                        if (FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated) {
                          onAddToCart(product, quantity);
                          toast.success('Producto añadido al pedido');
                        } else {
                          toast.error('Debes iniciar sesión para agregar productos al carrito');
                        }
                      }}
                      className="flex-1 bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg"
                    >
                      {isAuthenticated ? 'Agregar a la orden' : 'Agregar para cotizar'}
                    </button>
                  </div>
                ) : (
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <p className="text-secondary text-lg mb-4">
                      Inicia sesión para ver precios y realizar pedidos
                    </p>
                    <button
                      onClick={() => navigate('/login')}
                      className="w-full bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg"
                    >
                      Iniciar sesión
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Tabs Section - Fondo blanco */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
              <nav className="flex border-b border-gray-200">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 px-6 py-4 text-base font-medium transition-all duration-200
                      ${activeTab === tab.id
                        ? 'text-primary border-b-2 border-primary bg-gray-50'
                        : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                      }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
              <div className="p-6">
                {activeTab === 'características' && (
                  <div className="prose prose-lg max-w-none">
                    <p className="text-secondary leading-relaxed">{product.características || 'No hay información disponible'}</p>
                  </div>
                )}
                {activeTab === 'especificaciones' && (
                  <div className="prose prose-lg max-w-none">
                    <p className="text-secondary leading-relaxed">{product.especificaciones || 'No hay información disponible'}</p>
                  </div>
                )}
                {activeTab === 'presentación' && (
                  <div className="prose prose-lg max-w-none">
                    <p className="text-secondary leading-relaxed">{product.presentacion || 'No hay información disponible'}</p>
                  </div>
                )}
                {activeTab === 'documentación' && (
                  <div className="space-y-4">
                    {product.documentacion ? (
                      <a
                        href={product.documentacion}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-3 text-primary hover:text-primary-dark
                                 bg-gray-50 px-4 py-3 rounded-lg transition-colors duration-200"
                      >
                        <Download size={24} />
                        <span className="text-lg font-medium">Descargar documentación</span>
                      </a>
                    ) : (
                      <p className="text-secondary text-lg">No hay documentación disponible</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
