import express from 'express';
import { checkRole } from '../middleware/roleAuth';

const router = express.Router();

// Rutas de administración - hacer más específica la protección
router.use('/admin/*', checkRole(['admin']));

// Rutas de reportes
router.use('/reports', checkRole(['medium', 'large', 'admin']));

// Rutas de órdenes
router.use('/orders', checkRole(['small', 'medium', 'large', 'admin']));

export default router;
