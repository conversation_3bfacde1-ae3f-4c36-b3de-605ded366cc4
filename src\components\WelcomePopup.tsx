import React, { useState } from 'react';
import { Building2, X } from 'lucide-react';

interface WelcomePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { companyType: string; industry: string }) => void;
}

const companyTypes = [
  "Pequeña Empresa",
  "Mediana Empresa",
  "Gran Corporación",
  "Agencia Gubernamental",
  "Organización Sin Fines de Lucro"
];

const industries = [
  "Fábricas",
  "Salud",
  "Servicios Financieros",
  "Logística",
  "Educación",
  "Comercial",
  "Aviación",
  "Reventa",
  "Otros"
];

const WelcomePopup: React.FC<WelcomePopupProps> = ({ isOpen, onClose, onSubmit }) => {
  const [companyType, setCompanyType] = useState('');
  const [industry, setIndustry] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ companyType, industry });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="text-center mb-8">
          <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <Building2 className="w-8 h-8 text-blue-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Bienvenido a CR Seguridad Industrial</h2>
          <p className="text-gray-600">
            Ayúdanos a personalizar tu experiencia contándonos sobre tu organización
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de empresa
            </label>
            <select
              value={companyType}
              onChange={(e) => setCompanyType(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Seleccione tipo de empresa</option>
              {companyTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Industria
            </label>
            <select
              value={industry}
              onChange={(e) => setIndustry(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Seleccione Industria</option>
              {industries.map((ind) => (
                <option key={ind} value={ind}>{ind}</option>
              ))}
            </select>
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
          >
            Iniciemos
          </button>
        </form>
      </div>
    </div>
  );
};

export default WelcomePopup;
