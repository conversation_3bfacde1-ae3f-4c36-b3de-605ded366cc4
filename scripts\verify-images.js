import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function verifyAndFixImages() {
  try {
    // Obtener todos los productos
    const { data: products, error } = await supabase
      .from('products')
      .select('id, image_url');

    if (error) throw error;

    console.log(`Verificando ${products.length} productos...`);

    for (const product of products) {
      try {
        if (!product.image_url) {
          console.log(`Producto ${product.id}: Sin imagen`);
          continue;
        }

        // Verificar si la imagen existe en el bucket
        const { data, error: storageError } = await supabase.storage
          .from('product-images')
          .list(`${product.id}`);

        if (storageError) {
          console.error(`Error al verificar producto ${product.id}:`, storageError);
          continue;
        }

        if (!data || data.length === 0) {
          console.log(`Producto ${product.id}: Imagen no encontrada en storage`);
          // Actualizar producto para usar imagen por defecto
          await supabase
            .from('products')
            .update({ image_url: null })
            .eq('id', product.id);
        } else {
          // Actualizar con la URL correcta
          const fileName = data[0].name;
          const path = `${product.id}/${fileName}`;
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(path);

          await supabase
            .from('products')
            .update({ image_url: path })
            .eq('id', product.id);

          console.log(`✅ Producto ${product.id}: URL actualizada`);
        }
      } catch (err) {
        console.error(`Error procesando producto ${product.id}:`, err);
      }
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

verifyAndFixImages();