import { VercelRequest, VercelResponse } from '@vercel/node';
import { createClient } from '@supabase/supabase-js';

// Crear cliente Supabase una sola vez
const supabase = createClient(
  process.env.VITE_SUPABASE_URL!,
  process.env.VITE_SUPABASE_ANON_KEY!
);

// Cache para reducir consultas a Supabase
let healthCache = {
  status: 'unknown',
  timestamp: '',
  database: 'unknown',
  message: '',
  lastChecked: 0
};

// Tiempo de caché: 60 segundos
const CACHE_TTL = 60 * 1000;

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Cache-Control header para permitir caché en el cliente
  res.setHeader('Cache-Control', 'public, max-age=60');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    const now = Date.now();
    
    // Usar caché si está disponible y es reciente
    if (healthCache.lastChecked > 0 && now - healthCache.lastChecked < CACHE_TTL) {
      return res.status(200).json({
        ...healthCache,
        cached: true
      });
    }
    
    try {
      // Test Supabase connection
      const { data, error } = await supabase.from('products').select('id').limit(1);
      
      // Actualizar caché
      healthCache = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: error ? 'unhealthy' : 'healthy',
        message: error ? error.message : 'All systems operational',
        lastChecked: now
      };

      res.status(200).json(healthCache);
    } catch (error) {
      const errorResponse = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Service unavailable',
        lastChecked: now
      };
      
      // Actualizar caché incluso para errores
      healthCache = {
        ...errorResponse,
        database: 'unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
      
      res.status(503).json(errorResponse);
    }
  } else {
    res.setHeader('Allow', ['GET', 'OPTIONS']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
