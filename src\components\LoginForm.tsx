import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-toastify';
import { supabase } from '../lib/supabase';
import { Eye, EyeOff } from 'lucide-react';

const LoginForm = ({ setShowLogin, onSwitchToRegister }) => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [emailNotConfirmed, setEmailNotConfirmed] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [rememberMe, setRememberMe] = useState(false);
    const { login } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    const handleResendVerification = async () => {
        if (!email) {
            toast.warning('Por favor, ingresa tu correo electrónico');
            return;
        }

        try {
            const { error } = await supabase.auth.resend({
                type: 'signup',
                email: email
            });

            if (error) throw error;
            toast.success('Se ha enviado un nuevo correo de verificación');
        } catch (error) {
            console.error('Error al reenviar verificación:', error);
            toast.error('Error al enviar el correo de verificación');
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setEmailNotConfirmed(false);

        try {
            // 1. Authenticate with Supabase
            const { data: { session }, error } = await supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) {
                if (error.message === 'Email not confirmed') {
                    setEmailNotConfirmed(true);
                }
                throw error;
            }

            if (!session) {
                throw new Error('No session returned from Supabase');
            }

            // 2. Update local auth state
            const loginResult = await login(session.access_token, session.user);
            
            if (!loginResult.success) {
                throw loginResult.error;
            }

            // 3. Success handling
            toast.success('Inicio de sesión exitoso');
            setShowLogin(false);

            // 4. Navigation
            if (location.state?.from) {
                navigate(location.state.from.pathname, { replace: true });
            } else {
                navigate('/');
            }

        } catch (error) {
            console.error('Login error:', error);
            
            if (error.message === 'Email not confirmed') {
                toast.warning('Por favor verifica tu correo electrónico');
                setEmailNotConfirmed(true);
            } else if (error.message === 'Invalid login credentials') {
                toast.error('Credenciales inválidas');
            } else {
                toast.error('Error al iniciar sesión');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleForgotPassword = async () => {
        if (!email) {
            toast.warning('Por favor, ingresa tu correo electrónico');
            return;
        }

        try {
            const { error } = await supabase.auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/reset-password`,
            });

            if (error) throw error;

            toast.success('Se ha enviado un enlace de recuperación a tu correo');
        } catch (error) {
            console.error('Error al enviar email de recuperación:', error);
            toast.error('Error al enviar email de recuperación');
        }
    };

    return (
        <div className="w-full max-w-md">
            <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Correo electrónico
                    </label>
                    <div className="mt-1">
                        <input
                            id="email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                            required
                        />
                    </div>
                </div>

                {emailNotConfirmed && (
                    <div className="rounded-md bg-yellow-50 p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-yellow-800">
                                    Correo no verificado
                                </h3>
                                <div className="mt-2 text-sm text-yellow-700">
                                    <p>
                                        Por favor verifica tu correo electrónico para poder iniciar sesión.
                                        ¿No recibiste el correo?{' '}
                                        <button
                                            type="button"
                                            onClick={handleResendVerification}
                                            className="font-medium text-yellow-800 underline hover:text-yellow-600"
                                        >
                                            Reenviar verificación
                                        </button>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Contraseña
                    </label>
                    <div className="mt-1 relative">
                        <input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                            required
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                            {showPassword ? (
                                <EyeOff className="h-5 w-5 text-gray-400" />
                            ) : (
                                <Eye className="h-5 w-5 text-gray-400" />
                            )}
                        </button>
                    </div>
                </div>

                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <input
                            id="remember-me"
                            type="checkbox"
                            checked={rememberMe}
                            onChange={(e) => setRememberMe(e.target.checked)}
                            className="h-4 w-4 text-amber-500 focus:ring-amber-500 border-gray-300 rounded"
                        />
                        <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                            Recordarme
                        </label>
                    </div>

                    <button
                        type="button"
                        onClick={handleForgotPassword}
                        className="text-sm font-medium text-amber-600 hover:text-amber-500"
                    >
                        ¿Olvidaste tu contraseña?
                    </button>
                </div>

                <div>
                    <button
                        type="submit"
                        disabled={isLoading}
                        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? (
                            <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Iniciando sesión...
                            </span>
                        ) : (
                            'Iniciar Sesión'
                        )}
                    </button>
                </div>
            </form>

            <div className="mt-6">
                <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                        <span className="px-2 bg-white text-gray-500">
                            ¿No tienes una cuenta?
                        </span>
                    </div>
                </div>

                <div className="mt-6">
                    <button
                        type="button"
                        onClick={onSwitchToRegister}
                        className="w-full flex justify-center py-2 px-4 border border-amber-500 rounded-md shadow-sm text-sm font-medium text-amber-500 bg-white hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                    >
                        Crear cuenta nueva
                    </button>
                </div>
            </div>
        </div>
    );
};

export default LoginForm;





