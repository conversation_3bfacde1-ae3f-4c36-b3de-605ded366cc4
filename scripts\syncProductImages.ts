import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function syncProductImages() {
  try {
    console.log('Starting image URL synchronization...');
    
    // 1. Obtener todos los productos
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, image_url');

    if (productsError) throw productsError;
    
    console.log(`Found ${products.length} products to process`);
    
    let updated = 0;
    let skipped = 0;

    for (const product of products) {
      try {
        // 2. Buscar archivos main.* en el bucket para este producto
        const { data: files, error: listError } = await supabase.storage
          .from('product-images')
          .list(product.id, {
            search: 'main.'
          });

        if (listError) throw listError;

        if (files && files.length > 0) {
          // 3. Obtener la URL pública del primer archivo encontrado
          const mainFile = files[0];
          const path = `${product.id}/${mainFile.name}`;
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(path);

          // 4. Actualizar la URL en la base de datos si es diferente
          if (product.image_url !== publicUrl) {
            const { error: updateError } = await supabase
              .from('products')
              .update({ image_url: publicUrl })
              .eq('id', product.id);

            if (updateError) throw updateError;

            console.log(`✅ Updated URL for product ${product.id}`);
            updated++;
          } else {
            skipped++;
          }
        } else {
          console.log(`⚠️ No main image found for product ${product.id}`);
          skipped++;
        }
      } catch (err) {
        console.error(`❌ Error processing product ${product.id}:`, err);
      }
    }

    console.log('\n=== Synchronization Summary ===');
    console.log(`Total products processed: ${products.length}`);
    console.log(`URLs updated: ${updated}`);
    console.log(`Products skipped: ${skipped}`);

  } catch (error) {
    console.error('Synchronization error:', error);
    process.exit(1);
  }
}

syncProductImages();