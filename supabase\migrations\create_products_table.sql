-- Habilitar la extensión para búsqueda de texto
create extension if not exists pg_trgm;

-- Create the products table
create table public.products (
    id uuid default gen_random_uuid() primary key,
    name text not null,
    description text,
    price numeric not null,
    categories text[] not null default '{}',  -- Array de categorías
    industries text[] default '{}',           -- Array de industrias
    icon text,
    brand text,
    stock integer default 0,
    image_url text,
    caracteristicas text,
    especificaciones text,
    presentacion text,
    documentacion text,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now()
);

-- Create indexes for faster searches
create index products_name_idx on public.products using gin (name gin_trgm_ops);
create index idx_products_categories on public.products using gin (categories);
create index idx_products_industries on public.products using gin (industries);
create index products_brand_idx on public.products (brand);

-- Enable Row Level Security (RLS)
alter table public.products enable row level security;

-- Create policy for public read access
create policy "Products are viewable by everyone" 
on public.products for select 
to public 
using (true);

-- Create policy for authenticated users to modify products
create policy "Authenticated users can modify products" 
on public.products for all 
to authenticated 
using (true);

-- Trigger para actualizar updated_at
create or replace function update_updated_at_column()
returns trigger as $$
begin
    new.updated_at = now();
    return new;
end;
$$ language plpgsql;

create trigger update_products_updated_at
    before update on public.products
    for each row
    execute function update_updated_at_column();
