import { categoryIconMap } from '../components/PPEIcons';

// Tipos para las categorías de EPP
export type PPECategoryType = 
  'craneana' | 
  'ocular' | 
  'facial' | 
  'respiratoria' | 
  'indumentaria' | 
  'manos' | 
  'calzado' | 
  'auditiva' | 
  'contra-fuego' | 
  'altura';

// Interfaz para subcategorías de EPP
export interface PPESubcategory {
  id: string;
  name: string;
  description: string;
  standard?: string;
}

// Interfaz para categorías de EPP
export interface PPECategory {
  id: PPECategoryType;
  name: string;
  icon: any;
  description: string;
  subcategories: PPESubcategory[];
}

// Categorías de EPP
export const ppeCategories: PPECategory[] = [
  {
    id: 'craneana',
    name: 'Protección Craneana',
    icon: categoryIconMap['craneana'],
    description: 'Protección para la cabeza contra impactos y riesgos eléctricos',
    subcategories: [
      {
        id: 'dielectricos',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Protección contra riesgos eléctricos',
        standard: 'ANSI Z89.1'
      },
      {
        id: 'altura',
        name: '<PERSON><PERSON>s de Altura',
        description: 'Protección para trabajos en altura',
        standard: 'EN 397'
      },
      {
        id: 'gorra-casquete',
        name: 'Gorra con Casquete',
        description: 'Protección ligera para entornos de bajo riesgo',
        standard: 'EN 812'
      },
      {
        id: 'mentoneras',
        name: 'Mentoneras',
        description: 'Accesorios para sujeción de cascos',
        standard: 'EN 397'
      }
    ]
  },
  {
    id: 'ocular',
    name: 'Protección Ocular',
    icon: categoryIconMap['ocular'],
    description: 'Protección para los ojos contra partículas, salpicaduras y radiación',
    subcategories: [
      {
        id: 'anteojos',
        name: 'Anteojos de Seguridad',
        description: 'Protección contra impactos y radiación UV',
        standard: 'ANSI Z87.1'
      },
      {
        id: 'antiparras',
        name: 'Antiparras',
        description: 'Protección hermética contra salpicaduras químicas',
        standard: 'EN 166'
      }
    ]
  },
  {
    id: 'facial',
    name: 'Protección Facial',
    icon: categoryIconMap['facial'],
    description: 'Protección completa para el rostro contra diversos riesgos',
    subcategories: [
      {
        id: 'plano',
        name: 'Protector Facial Plano',
        description: 'Protección básica contra impactos',
        standard: 'ANSI Z87.1'
      },
      {
        id: 'burbuja',
        name: 'Protector Facial Burbuja',
        description: 'Protección envolvente contra salpicaduras',
        standard: 'EN 166'
      },
      {
        id: 'forestal',
        name: 'Protector Facial Forestal',
        description: 'Protección para trabajos forestales',
        standard: 'EN 1731'
      },
      {
        id: 'deflagatoria',
        name: 'Protección Deflagatoria',
        description: 'Protección contra arco eléctrico',
        standard: 'NFPA 70E'
      },
      {
        id: 'soldador',
        name: 'Careta de Soldador',
        description: 'Protección contra radiación y chispas',
        standard: 'ANSI Z87.1'
      }
    ]
  },
  {
    id: 'respiratoria',
    name: 'Protección Respiratoria',
    icon: categoryIconMap['respiratoria'],
    description: 'Protección para las vías respiratorias contra partículas, gases y vapores',
    subcategories: [
      {
        id: 'mascarillas-descartables',
        name: 'Mascarillas Descartables',
        description: 'Protección contra partículas y aerosoles',
        standard: 'NIOSH N95'
      },
      {
        id: 'mascaras',
        name: 'Máscaras Respiratorias',
        description: 'Protección contra gases y vapores',
        standard: 'EN 14387'
      },
      {
        id: 'equipos-autonomos',
        name: 'Equipos Autónomos',
        description: 'Suministro de aire para ambientes peligrosos',
        standard: 'NFPA 1981'
      }
    ]
  },
  {
    id: 'indumentaria',
    name: 'Indumentaria',
    icon: categoryIconMap['indumentaria'],
    description: 'Ropa de protección para diversos entornos laborales',
    subcategories: [
      {
        id: 'camisas-remeras',
        name: 'Camisas y Remeras',
        description: 'Indumentaria para torso superior (camisas, remeras y chombas)',
        standard: 'EN ISO 13688'
      },
      {
        id: 'pantalones',
        name: 'Pantalones',
        description: 'Protección para extremidades inferiores',
        standard: 'EN ISO 13688'
      },
      {
        id: 'camperas',
        name: 'Camperas',
        description: 'Protección contra clima adverso',
        standard: 'EN 342'
      },
      {
        id: 'buzos',
        name: 'Buzos',
        description: 'Protección térmica y comodidad',
        standard: 'EN ISO 13688'
      },
      {
        id: 'termica',
        name: 'Indumentaria Térmica',
        description: 'Protección contra temperaturas extremas',
        standard: 'EN 342'
      },
      {
        id: 'estampados',
        name: 'Estampados y Bordados',
        description: 'Personalización de indumentaria',
        standard: ''
      },
      {
        id: 'mamelucos',
        name: 'Mamelucos',
        description: 'Protección corporal completa',
        standard: 'EN ISO 13688'
      },
      {
        id: 'descartables',
        name: 'Indumentaria Descartable',
        description: 'Protección de un solo uso',
        standard: 'EN 14126'
      }
    ]
  },
  {
    id: 'manos',
    name: 'Protección para Manos',
    icon: categoryIconMap['manos'],
    description: 'Protección para las manos contra diversos riesgos',
    subcategories: [
      {
        id: 'anticorte',
        name: 'Guantes Anticorte',
        description: 'Protección contra cortes y abrasiones',
        standard: 'EN 388'
      },
      {
        id: 'temperatura',
        name: 'Guantes para Temperatura',
        description: 'Protección contra calor o frío extremo',
        standard: 'EN 407/EN 511'
      },
      {
        id: 'impacto',
        name: 'Guantes Anti-impacto',
        description: 'Protección contra golpes y vibraciones',
        standard: 'EN 388'
      },
      {
        id: 'nitrilicos',
        name: 'Guantes Nitrílicos',
        description: 'Protección química y biológica',
        standard: 'EN 374'
      },
      {
        id: 'latex',
        name: 'Guantes de Látex',
        description: 'Protección para manipulación general',
        standard: 'EN 374'
      },
      {
        id: 'dielectricos',
        name: 'Guantes Dieléctricos',
        description: 'Protección contra riesgos eléctricos',
        standard: 'ASTM D120'
      },
      {
        id: 'descarne',
        name: 'Guantes de Descarne',
        description: 'Protección para trabajos pesados',
        standard: 'EN 388'
      },
      {
        id: 'mangas',
        name: 'Mangas Protectoras',
        description: 'Protección para brazos',
        standard: 'EN 388'
      }
    ]
  },
  {
    id: 'calzado',
    name: 'Calzado de Seguridad',
    icon: categoryIconMap['calzado'],
    description: 'Protección para los pies contra impactos, perforaciones y riesgos eléctricos',
    subcategories: [
      {
        id: 'zapatos',
        name: 'Zapatos de Seguridad',
        description: 'Protección básica para entornos industriales',
        standard: 'ASTM F2413'
      },
      {
        id: 'botinas',
        name: 'Botinas',
        description: 'Protección con soporte para tobillos',
        standard: 'EN ISO 20345'
      },
      {
        id: 'zapatillas',
        name: 'Zapatillas de Seguridad',
        description: 'Protección ligera y cómoda',
        standard: 'EN ISO 20345'
      },
      {
        id: 'ultralivianos',
        name: 'Calzado Ultraliviano',
        description: 'Máxima comodidad con protección',
        standard: 'EN ISO 20345'
      },
      {
        id: 'botas-pvc',
        name: 'Botas PVC',
        description: 'Protección contra humedad y químicos',
        standard: 'EN ISO 20345'
      }
    ]
  },
  {
    id: 'auditiva',
    name: 'Protección Auditiva',
    icon: categoryIconMap['auditiva'],
    description: 'Protección para los oídos contra ruidos perjudiciales',
    subcategories: [
      {
        id: 'expansibles',
        name: 'Expansibles Endoaurales',
        description: 'Protección interna para los oídos',
        standard: 'ANSI S3.19'
      },
      {
        id: 'copa-vincha',
        name: 'Protectores de Copa con Vincha',
        description: 'Protección externa con soporte sobre la cabeza',
        standard: 'ANSI S3.19'
      },
      {
        id: 'para-cascos',
        name: 'Protectores para Cascos',
        description: 'Protección auditiva adaptable a cascos',
        standard: 'ANSI S3.19'
      }
    ]
  },
  {
    id: 'contra-fuego',
    name: 'Protección Contra Fuego',
    icon: categoryIconMap['contra-fuego'],
    description: 'Protección contra incendios y altas temperaturas',
    subcategories: [
      {
        id: 'indumentaria-fuego',
        name: 'Indumentaria Ignífuga',
        description: 'Ropa resistente al fuego',
        standard: 'NFPA 2112'
      },
      {
        id: 'extincion',
        name: 'Equipos de Extinción',
        description: 'Herramientas para combatir incendios',
        standard: 'NFPA 1971'
      }
    ]
  },
  {
    id: 'altura',
    name: 'Protección para Altura',
    icon: categoryIconMap['altura'],
    description: 'Equipos para trabajos en altura y prevención de caídas',
    subcategories: [
      {
        id: 'arnes',
        name: 'Arnés de Seguridad',
        description: 'Sistema de sujeción corporal',
        standard: 'ANSI Z359.11'
      },
      {
        id: 'cabo-vida',
        name: 'Cabo de Vida',
        description: 'Línea de conexión para arnés',
        standard: 'ANSI Z359.13'
      },
      {
        id: 'anticaida',
        name: 'Dispositivos Anticaída',
        description: 'Sistemas de detención de caídas',
        standard: 'ANSI Z359.14'
      },
      {
        id: 'antitrauma',
        name: 'Equipos Antitrauma',
        description: 'Prevención de trauma por suspensión',
        standard: 'ANSI Z359.11'
      }
    ]
  }
];

// Función para obtener todas las subcategorías
export const getAllSubcategories = () => {
  const allSubcategories: (PPESubcategory & { categoryId: PPECategoryType, categoryName: string })[] = [];
  
  ppeCategories.forEach(category => {
    category.subcategories.forEach(subcategory => {
      allSubcategories.push({
        ...subcategory,
        categoryId: category.id,
        categoryName: category.name
      });
    });
  });
  
  return allSubcategories;
};