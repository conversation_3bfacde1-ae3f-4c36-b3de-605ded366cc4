/**
 * Mapea IDs de industria a una lista de IDs de categorías/subcategorías relevantes
 * de `catalogStructure`. Estos IDs de categoría se usarán para filtrar y mostrar
 * las opciones de filtrado secundario cuando se selecciona una industria.
 *
 * Las claves deben coincidir con los identificadores de industria que usas
 * (ej. los que vienen de `allIndustries` en `IndustryFilter` o `selectedIndustries` en `Catalog.tsx`).
 * Los valores son arrays de strings, donde cada string es un `id` de `CatalogCategoryNode`.
 */
export const industryCategoryMapping: Record<string, string[]> = {
  'MINERIA': [
    'craneana',    // Cascos
    'respiratoria',// Respiradores
    'auditiva',
    'ocular',
    'manos',       // Guantes
    'calzado',       // Calzado
    'indumentaria',  // Indumentaria específica para minería si aplica
    'altura-epp',          // Si aplica (ahora bajo epp)
  ],
  'CONSTRUCCION': [
    'craneana',
    'ocular',
    'facial',
    'auditiva',
    'manos',
    'indumentaria',
    'calzado',
    'altura-epp',          // Arneses, etc. (ahora bajo epp)
    'seguridad-emergencias',  // Señalización, etc.
  ],
  'MANUFACTURA': [
    'manos',
    'respiratoria', // Mascarillas
    'auditiva',
    'ocular',
    'indumentaria',
  ],
  'PETROLERA': [
    'craneana',
    'respiratoria',
    'contra-fuego', // Trajes ignífugos (ahora bajo epp)
    'manos',
    'calzado',
  ],
  'QUIMICA': [
    'respiratoria',
    'ocular',
    'facial',
    'manos', // Guantes químicos
    'indumentaria', // Trajes químicos (puede ser 'indumentaria-descartables' si es más específico)
    // 'indumentaria-descartables' es una subcategoría de 'indumentaria'
  ],
  'METALMECANICA': [
    'facial',    // Caretas de soldador
    'ocular',
    'auditiva',
    'manos',
    'indumentaria', // Delantales, etc.
  ],
  'LOGISTICA': [
    // 'Fajas' no es una categoría directa en catalogStructure, podría ser un 'accesorio' o parte de 'proteccion-corporal'
    // o un producto específico dentro de 'corporal-indumentaria'.
    // Por ahora, mapeamos a categorías más generales.
    'proteccion-manos',
    'calzado',
    'accesorios', // Podría incluir fajas si se clasifican así en 'accesorios'
  ],
  // Añade aquí el resto de tus industrias y sus categorías asociadas
  // 'AGRICOLA': [],
  // 'ALIMENTICIA': [],
  // 'SALUD': [],
  // ... etc.
};

/**
 * Función para obtener los nodos de categoría (CatalogCategoryNode) relevantes
 * para una lista de industrias seleccionadas, basándose en el mapeo anterior
 * y en la catalogStructure completa.
 * 
 * @param selectedIndustryNames Nombres/IDs de las industrias seleccionadas.
 * @param fullCatalogStructure La estructura completa del catálogo.
 * @param currentMapping El mapeo de industria a IDs de categoría.
 * @returns Un array de CatalogCategoryNode que son relevantes para las industrias dadas.
 *          Retorna solo los nodos de primer nivel que coinciden o cuyos hijos coinciden.
 */
import type { CatalogCategoryNode } from '../types/catalog'; // Ruta corregida

export function getRelevantCategoriesForIndustries(
  selectedIndustryNames: string[],
  fullCatalogStructure: CatalogCategoryNode[],
  currentMapping: Record<string, string[]> = industryCategoryMapping
): CatalogCategoryNode[] {
  if (selectedIndustryNames.length === 0) {
    return []; // O podrías retornar fullCatalogStructure si no hay industria seleccionada
  }

  const relevantCategoryIds = new Set<string>();
  selectedIndustryNames.forEach(industryName => {
    const mappedIds = currentMapping[industryName.toUpperCase()]; // Asumir que las claves del mapping son mayúsculas
    if (mappedIds) {
      mappedIds.forEach(id => relevantCategoryIds.add(id));
    }
  });

  if (relevantCategoryIds.size === 0) {
    return [];
  }

  // Filtrar fullCatalogStructure para obtener solo los nodos cuyos IDs (o IDs de sus hijos recursivamente)
  // están en relevantCategoryIds. Esto es complejo si queremos mantener la jerarquía.
  // Una simplificación: retornar los nodos de primer nivel de fullCatalogStructure
  // que están en relevantCategoryIds o que tienen hijos en relevantCategoryIds.

  const result: CatalogCategoryNode[] = [];
  const seenTopLevelIds = new Set<string>();

  function findAndAddNodes(nodes: CatalogCategoryNode[], parentArray: CatalogCategoryNode[]) {
    for (const node of nodes) {
      let nodeIsRelevant = relevantCategoryIds.has(node.id);
      let relevantChildren: CatalogCategoryNode[] | undefined = undefined;

      if (node.children) {
        const filteredChildren: CatalogCategoryNode[] = [];
        findAndAddNodes(node.children, filteredChildren); // Recursión
        if (filteredChildren.length > 0) {
          relevantChildren = filteredChildren;
          nodeIsRelevant = true; // Un nodo padre es relevante si alguno de sus hijos lo es
        }
      }
      
      if (nodeIsRelevant && !seenTopLevelIds.has(node.id) && nodes === fullCatalogStructure) { // Solo añadir nodos de primer nivel una vez
        parentArray.push({ ...node, children: relevantChildren || node.children }); // Preservar hijos originales si no hay filtro de hijos
        seenTopLevelIds.add(node.id);
      } else if (nodeIsRelevant && nodes !== fullCatalogStructure) { // Para hijos
         parentArray.push({ ...node, children: relevantChildren || node.children });
      }
    }
  }
  
  // Alternativa más simple: devolver solo los nodos de `fullCatalogStructure` cuyos IDs están en `relevantCategoryIds`
  // Esto no manejaría bien si un ID relevante es de una subcategoría profunda y queremos mostrar su padre.
  // La lógica de arriba intenta ser un poco más inteligente, pero puede necesitar refinamiento.

  // Por ahora, una implementación más directa para obtener los nodos de categoría basados en los IDs relevantes:
  const finalRelevantNodes: CatalogCategoryNode[] = [];
  const collectNodesById = (nodes: CatalogCategoryNode[], ids: Set<string>, collected: CatalogCategoryNode[]) => {
    for (const node of nodes) {
      if (ids.has(node.id)) {
        // Clonar el nodo para no modificar la estructura original si se filtran hijos
        const clonedNode = { ...node };
        // Si queremos que solo se muestren los hijos también relevantes:
        // if (node.children) {
        //   const relevantChildrenOfNode: CatalogCategoryNode[] = [];
        //   collectNodesById(node.children, ids, relevantChildrenOfNode);
        //   clonedNode.children = relevantChildrenOfNode.length > 0 ? relevantChildrenOfNode : undefined;
        // }
        collected.push(clonedNode);
      } else if (node.children) {
        // Si el padre no está en la lista pero los hijos podrían estarlo (para mostrar la jerarquía)
        // Esta parte es compleja y depende de cómo se quiera la UI.
        // Por simplicidad, si el ID del nodo no está en relevantCategoryIds, no lo incluimos directamente.
        // La lógica de filtrado en el componente UI se encargará de mostrar la jerarquía.
      }
    }
  };
  
  // Para la UI del filtro, es mejor pasar la estructura completa y que el componente de UI
  // (HierarchicalCategoryFilterSection) sepa qué IDs resaltar o habilitar basado en relevantCategoryIds.
  // O, construir una sub-estructura filtrada.
  
  // Vamos a construir una sub-estructura filtrada:
  const buildFilteredStructure = (nodes: CatalogCategoryNode[], allowedIds: Set<string>): CatalogCategoryNode[] => {
    return nodes.map(node => ({ ...node })).filter(node => {
      if (allowedIds.has(node.id)) {
        if (node.children) {
          node.children = buildFilteredStructure(node.children, allowedIds);
        }
        return true;
      } else if (node.children) {
        const filteredChildren = buildFilteredStructure(node.children, allowedIds);
        if (filteredChildren.length > 0) {
          node.children = filteredChildren;
          return true; // El padre es relevante si tiene hijos relevantes
        }
      }
      return false;
    });
  };

  if (relevantCategoryIds.size > 0) {
    return buildFilteredStructure(fullCatalogStructure, relevantCategoryIds);
  }
  return [];
}