import React from 'react';
import { CategoryCard, ServiceCard } from './CategoryCard';
import { 
  ShoppingBag, 
  Shield, 
  HardHat, 
  Wrench, // Cambiado de Tool a Wrench
  Truck, 
  Users,
  Star,
  Clock
} from 'lucide-react';
import './CategoriesGrid.css';

interface Category {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  variant?: 'primary' | 'secondary' | 'featured';
}

const categories: Category[] = [
  {
    title: 'Equipos de Protección',
    description: 'EPP certificado para todas las industrias',
    icon: Shield,
    variant: 'featured'
  },
  {
    title: 'Seguridad Industrial',
    description: 'Elementos y equipos de seguridad',
    icon: HardHat
  },
  {
    title: 'Herramientas',
    description: 'Herramientas profesionales y accesorios',
    icon: Wrench  // Cambiado de Tool a Wrench
  },
  {
    title: 'Indumentaria',
    description: 'Ropa de trabajo y uniformes',
    icon: ShoppingBag
  }
];

const services: Category[] = [
  {
    title: 'Entrega Express',
    description: 'Envíos en 24 horas a todo el país',
    icon: Truck,
    variant: 'primary'
  },
  {
    title: 'Asesoría Técnica',
    description: 'Equipo especializado a tu disposición',
    icon: Users,
    variant: 'secondary'
  },
  {
    title: 'Productos Certificados',
    description: 'Garantía de calidad y seguridad',
    icon: Star,
    variant: 'primary'
  },
  {
    title: 'Soporte 24/7',
    description: 'Atención permanente para emergencias',
    icon: Clock,
    variant: 'secondary'
  }
];

export const CategoriesGrid: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Sección de Categorías */}
      <section 
        className="w-full py-16 relative bg-amber-500" 
        style={{ 
          backgroundColor: '#F59E0B',  // amber-500
          boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)'
        }}
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-white mb-8">
            Nuestras Categorías
          </h2>
          <div className="category-grid">
            {categories.map((category, index) => (
              <CategoryCard
                key={index}
                title={category.title}
                description={category.description}
                icon={category.icon}
                variant={category.variant}
                onClick={() => console.log(`Clicked: ${category.title}`)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Sección de Servicios */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Servicios Destacados
          </h2>
          <div className="services-grid">
            {services.map((service, index) => (
              <ServiceCard
                key={index}
                title={service.title}
                description={service.description}
                icon={service.icon}
                variant={service.variant}
                onClick={() => console.log(`Clicked: ${service.title}`)}
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};
