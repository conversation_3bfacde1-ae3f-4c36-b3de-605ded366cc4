.personal-protection-section {
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
    background-image: linear-gradient(to bottom, #f9fafb, #f3f4f6);
}

.image-container {
    position: relative;
    max-width: 27.5%;
    margin-bottom: 1rem;
    /* overflow: hidden; Para asegurar que la imagen recortada no se desborde */
}

.image-container img {
    display: block;
    width: 100%;
    height: auto;
    object-fit: cover;
    object-position: center 20%; /* Ajusta la posición vertical de la imagen */
}
  
.hotspot {
  position: absolute;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  transform: translate(-50%, -50%); /* Centra el punto respecto a sus coordenadas */
}

.hotspot:hover {
  background-color: rgba(59, 130, 246, 0.7); /* Un azul más visible al hover */
  transform: translate(-50%, -50%) scale(1.1); /* Efecto de escala al hover */
}
  
  .tooltip {
    position: absolute;
    z-index: 10; /* Ensure the tooltip is above the hotspots */
    white-space: nowrap; /* Prevent text from wrapping */
    font-size: 0.875rem;
  }
