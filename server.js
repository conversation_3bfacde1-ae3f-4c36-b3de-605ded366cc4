
import dotenv from 'dotenv';
dotenv.config();
import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import { authMiddleware } from './middleware/auth.js';
console.log('Supabase URL:', process.env.VITE_SUPABASE_URL);
console.log('Supabase Anon Key:', process.env.VITE_SUPABASE_ANON_KEY);
import { requestLogger } from './lib/logger.js';
import healthRoutes from './routes/health.js';
import logger from './lib/logger.js';

// Cargar variables de entorno
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Verificar variables de entorno críticas
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
];

const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  console.error('Please check your .env file');
  process.exit(1);
}

const app = express();

// Middleware básico
const corsOrigins = process.env.CORS_ORIGINS 
  ? process.env.CORS_ORIGINS.split(',') 
  : [
      'https://cr-seg-ind.pages.dev',
      'http://localhost:5173',
      'https://cr-work.up.railway.app/'
    ];

app.use(cors({
  origin: [
    'https://cr-seg-ind.pages.dev',
    'https://cr-seg-ind-production.up.railway.app',
    'https://cr-work.up.railway.app',
    'http://localhost:5173'
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());
app.use(requestLogger);

// Agregar después de los middlewares básicos
app.use(express.static('public'));
app.use('/images', express.static('public/images', {
  maxAge: '1d',
  setHeaders: (res, path) => {
    res.setHeader('Cache-Control', 'public, max-age=86400');
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));

// Para asegurarnos que las rutas de imágenes sean accesibles
app.get('/images/:filename', (req, res) => {
  const filename = req.params.filename;
  res.sendFile(path.join(__dirname, 'public', 'images', filename));
});

// Rutas públicas
app.use('/', healthRoutes);

// Rutas protegidas
app.use('/api', authMiddleware, (req, res, next) => {
  logger.debug('Authenticated request', { 
    user: req.user.id,
    path: req.path 
  });
  next();
});

// Manejo de errores global
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { 
    error: err.message,
    stack: err.stack,
    path: req.path
  });
  
  res.status(500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message
  });
});

// Inicializar Supabase solo si se necesita
let supabase;
try {
  supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.VITE_SUPABASE_ANON_KEY
  );
  console.log('Supabase client initialized successfully');
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

// API routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok',
    message: 'API is running',
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    supabaseInitialized: !!supabase
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    supabaseConnection: !!supabase
  });
});

// Manejar todas las rutas no-API enviando index.html
app.get('*', (req, res, next) => {
  // Si la ruta comienza con /api, continuar al siguiente middleware
  if (req.path.startsWith('/api')) {
    return next();
  }
  // Para todas las demás rutas, enviar index.html
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

const PORT = process.env.PORT || 3010;
app.listen(PORT, () => {
  logger.info(`Server started`, {
    port: PORT,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version
  });
});

// Manejo de señales de terminación
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Performing graceful shutdown...');
  // Implementar lógica de cierre graceful aquí
  process.exit(0);
});
