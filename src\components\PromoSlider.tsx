import React from 'react';
import { useNavigate } from 'react-router-dom';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { IMAGES } from '../config/constants';

const PromoSlider = () => {
  const navigate = useNavigate();

  const settings = {
    dots: true,
    infinite: true,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    arrows: false, // Removemos las flechas
    cssEase: "cubic-bezier(0.87, 0.03, 0.41, 0.9)",
  };

  const slides = [
    {
      image: IMAGES.PROMO_3M,
      title: "Toda la gama de productos 3M",
      description: "Calidad y seguridad en cada producto",
      buttonText: "Ver productos 3M",
      action: () => navigate('/catalog?brand=3M'),
    },
    {
      image: IMAGES.PROMO_RINGO,
      title: "¡Liquidación especial en productos Ringo!",
      description: "Hasta 30% OFF en productos seleccionados",
      buttonText: "Ver ofertas",
      action: () => navigate('/catalog?discount=true'),
    },
    {
      image: IMAGES.PROMO_EPP,
      title: "Equipos de Protección Personal",
      description: "Tu seguridad es nuestra prioridad",
      buttonText: "Explorar EPP",
      action: () => navigate('/catalog?category=EPP'),
    },
  ];

  return (
    <div className="relative w-screen mb-0 bg-slate-900"> {/* Añadido bg-slate-900 */}
      {/* Degradado superior incrementado */}
      <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-gray-900 to-transparent z-20 pointer-events-none" />
      
      <Slider {...settings} className="promo-slider">
        {slides.map((slide, index) => (
          <div key={index} className="relative">
            <div className="relative h-[calc(100vh-72px)] bg-black"> {/* Reducido de h-screen a h-[calc(100vh-72px)] */}
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-contain"
              />
              {/* Degradado optimizado del slide */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
              <div className="absolute inset-0 flex flex-col items-center justify-start pt-32 text-white text-center px-4">
                <h2 className="text-5xl font-bold mb-4">{slide.title}</h2>
                <p className="text-xl mb-8">{slide.description}</p>
                <button
                  onClick={slide.action}
                  className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg 
                           text-lg font-semibold transition-all duration-300 
                           transform hover:scale-105 hover:shadow-lg"
                >
                  {slide.buttonText}
                </button>
              </div>
            </div>
          </div>
        ))}
      </Slider>

      {/* Degradado inferior */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 via-gray-900/35 to-transparent pointer-events-none" />
    </div>
  );
};

export default PromoSlider;
