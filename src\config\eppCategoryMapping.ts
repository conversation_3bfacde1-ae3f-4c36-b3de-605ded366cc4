import { BodyPart } from './ppeData';

// Mapeo de partes del cuerpo EPP a categorías del catálogo
export const EPP_TO_CATALOG_MAPPING: Record<BodyPart, string> = {
  'head': 'PROTECCION_CABEZA',
  'face': 'PROTECCION_VISUAL', 
  'torso': 'PROTECCION_TORSO',
  'hands': 'PROTECCION_MANOS',
  'feet': 'PROTECCION_PIES',
  'respiratory': 'PROTECCION_RESPIRATORIA',
  'hearing': 'PROTECCION_AUDITIVA',
  'fall': 'PROTECCION_CAIDAS'
};

// Función para obtener la categoría del catálogo desde EPP
export const getEPPCatalogCategory = (bodyPart: BodyPart): string => {
  return EPP_TO_CATALOG_MAPPING[bodyPart] || bodyPart.toUpperCase();
};

// Función para obtener subcategorías EPP por categoría
export const getEPPSubcategoriesByCategory = (category: string): string[] => {
  return Object.entries(EPP_TO_CATALOG_MAPPING)
    .filter(([_, cat]) => cat === category)
    .map(([bodyPart]) => bodyPart);
};