/**
 * Mapea la combinación de una Industria (clave externa, ej. 'CONSTRUCCION') y 
 * el nombre de un "subfiltro" del IndustrySelector (clave interna, ej. 'Cascos')
 * al ID de una categoría o subcategoría de `catalogStructure`.
 * 
 * Las claves de industria deben estar en MAYÚSCULAS para coincidir con cómo se usan
 * (ej. selectedIndustryView.toUpperCase()).
 * Las claves de subfiltro deben coincidir con los `name` de los `SubfilterItem`
 * definidos en `src/config/industrySubfilters.ts`.
 */
export const selectorSubfilterToCatalogIdMap: Record<string, Record<string, string>> = {
  'CONSTRUCCION': {
    'Cascos': 'craneana', // ID de la categoría principal de cascos dentro de EPP
    'Arnés': 'altura-arnes', // ID específico de la subcategoría arnés
    'Chalecos': 'indumentaria', // ID de la categoría principal de indumentaria dentro de EPP
    'Guantes': 'manos', // ID de la categoría principal de guantes dentro de EPP
    'Calzado': 'calzado', // ID de la categoría principal de calzado dentro de EPP
    'Anteojos': 'ocular-anteojos', // ID específico de la subcategoría anteojos
  },
  'MINERIA': {
    'Cascos': 'craneana',
    'Guantes': 'manos',
    'Calzado': 'calzado',
    // Si 'Respiradores' es un subfiltro para Minería, mapearía a 'respiratoria' o una subcategoría específica.
  },
  'MANUFACTURA': {
    'Guantes': 'manos',
    'Mascarillas': 'respiratoria-mascarillas-descartables', // O 'respiratoria' si es más general
    'Protección auditiva': 'auditiva',
  },
  'PETROLERA': {
    'Trajes ignífugos': 'contrafuego-indumentaria', // ID específico de la subcategoría
    'Respiradores': 'respiratoria', // O una subcategoría específica como 'respiratoria-mascaras'
    'Cascos': 'craneana',
  },
  'QUIMICA': {
    'Trajes químicos': 'indumentaria-descartables', // O 'indumentaria' si es más general
    'Respiradores': 'respiratoria-mascaras', // O 'respiratoria'
    'Guantes químicos': 'manos-nitrilicos', // Ejemplo, podría ser otro tipo de guante químico
  },
  'METALMECANICA': {
    'Protección facial': 'facial-soldador', // O 'facial' si es más general
    'Guantes': 'manos-descarne', // Ejemplo, podría ser 'manos'
    'Delantales': 'indumentaria', // Asumiendo que es un tipo de indumentaria general
  },
  'LOGISTICA': {
    'Fajas': 'accesorios', // O una categoría más específica si existe en catalogStructure
    'Guantes': 'manos',
    'Calzado de seguridad': 'calzado',
  },
  // ... Completa este mapeo para todas las industrias y sus "subfiltros" del selector
  // Asegúrate de que los nombres de los subfiltros coincidan exactamente con los de `industrySubfilters.ts`
  // y que los IDs de categoría coincidan con los de `catalogStructure`.
};