import React from 'react';
import { 
  HardHat, 
  <PERSON>, 
  Smile, 
  Wind, 
  Shirt, 
  Hand, 
  Footprints, 
  Ear, 
  FireExtinguisher, 
  Ruler 
} from 'lucide-react';

// Interface for icon props
interface IconProps {
  className?: string;
}

// Craneana (Casco de seguridad)
export const HelmetIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <HardHat className={className} />
);

// Ocular (Lentes/Gafas de seguridad)
export const EyeProtectionIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Eye className={className} />
);

// Facial (Protector facial)
export const FaceShieldIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Smile className={className} />
);

// Respiratoria (Mascarilla)
export const RespiratoryIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Wind className={className} />
);

// Indumentaria (Chaleco/Protección corporal)
export const ClothingIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Shirt className={className} />
);

// Manos (Guantes)
export const GlovesIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Hand className={className} />
);

// Calzado (Botas de seguridad)
export const FootwearIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Footprints className={className} />
);

// Auditiva (Protección auditiva)
export const HearingProtectionIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Ear className={className} />
);

// Contra Fuego (Protección contra incendios)
export const FireProtectionIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <FireExtinguisher className={className} />
);

// Altura (Protección para trabajos en altura)
export const HeightProtectionIcon: React.FC<IconProps> = ({ className = "w-6 h-6" }) => (
  <Ruler className={className} />
);

// Map of category IDs to their respective icons
export const categoryIconMap = {
  'craneana': HelmetIcon,
  'ocular': EyeProtectionIcon,
  'facial': FaceShieldIcon,
  'respiratoria': RespiratoryIcon,
  'indumentaria': ClothingIcon,
  'manos': GlovesIcon,
  'calzado': FootwearIcon,
  'auditiva': HearingProtectionIcon,
  'contra-fuego': FireProtectionIcon,
  'altura': HeightProtectionIcon
};
