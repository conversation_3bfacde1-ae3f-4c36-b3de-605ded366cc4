-- Crear tabla para productos destacados
create table public.featured_products (
    id uuid default gen_random_uuid() primary key,
    product_id uuid references public.products(id) on delete cascade,
    featured_type text not null check (featured_type in ('featured', 'new', 'top')),
    start_date timestamp with time zone default now(),
    end_date timestamp with time zone,
    created_at timestamp with time zone default now(),
    created_by uuid references auth.users(id),
    
    -- Asegurar que no haya duplicados para el mismo producto y tipo
    unique(product_id, featured_type)
);

-- Índices para búsquedas eficientes
create index idx_featured_products_product_id on public.featured_products(product_id);
create index idx_featured_products_type on public.featured_products(featured_type);
create index idx_featured_products_dates on public.featured_products(start_date, end_date);

-- Habilitar RLS
alter table public.featured_products enable row level security;

-- Políticas de seguridad
create policy "Featured products are viewable by everyone"
    on public.featured_products for select
    to public
    using (true);

create policy "Only authenticated users can modify featured products"
    on public.featured_products for all
    to authenticated
    using (true);

-- Función para obtener productos destacados activos
create or replace function public.get_active_featured_products(
    feature_type text default null,
    limit_count integer default 4
)
returns table (
    id uuid,
    product_id uuid,
    featured_type text,
    product_data jsonb
) as $$
begin
    return query
    select 
        fp.id,
        fp.product_id,
        fp.featured_type,
        to_jsonb(p.*) as product_data
    from featured_products fp
    join products p on p.id = fp.product_id
    where 
        (feature_type is null or fp.featured_type = feature_type)
        and (fp.end_date is null or fp.end_date > now())
        and fp.start_date <= now()
    order by fp.created_at desc
    limit limit_count;
end;
$$ language plpgsql security definer;