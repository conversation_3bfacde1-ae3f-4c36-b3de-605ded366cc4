import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronUp, ChevronDown } from 'lucide-react';
import { getPublicImageUrl } from '../config/storage';
import { getSubfiltersForIndustry } from '../config/industrySubfilters';

interface IndustrySelectorProps {
  selectedIndustry: string | null;
  onIndustrySelect: (industry: string) => void;
  onBack: () => void;
  onSubfilterSelect?: (subfilter: string) => void; // Nueva prop para manejar selección de subfiltros
  selectedSubfilters?: string[]; // Add this prop to track selected subfilters
  isCollapsed?: boolean; // New prop to control collapse state externally
  onCollapseToggle?: (collapsed: boolean) => void; // New prop to notify parent of collapse changes
}

const IndustrySelector: React.FC<IndustrySelectorProps> = ({
  selectedIndustry,
  onIndustrySelect,
  onBack,
  onSubfilterSelect,
  selectedSubfilters = [],
  isCollapsed: externalCollapsed = false, // Default to expanded
  onCollapseToggle
}) => {
  // Use local state that syncs with external state
  const [isCollapsed, setIsCollapsed] = useState(externalCollapsed);
  
  // Update local state when external state changes
  useEffect(() => {
    setIsCollapsed(externalCollapsed);
  }, [externalCollapsed]);

  // Function to toggle collapse state
  const toggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    // Notify parent component
    if (onCollapseToggle) {
      onCollapseToggle(newCollapsedState);
    }
  };

  // Helper function to check if a subfilter is selected
  const isSubfilterSelected = (filterName: string) => {
    return selectedSubfilters?.includes(filterName) || false;
  };

  // La función getCardHeight no se usa, por lo que se elimina para evitar advertencias

  // Altura fija para las cards en la vista de todas las industrias
  const getIndustryCardHeight = () => {
    return "h-36";
  };

  // La función getIndustryCardWidth no se usa, por lo que se elimina para evitar advertencias

  // Function to get industry image from Supabase
  const getIndustryImage = (industryId: string): string => {
    // Format: static-images/industry-selector/{industry}-banner.jpg
    return getPublicImageUrl(`industry-selector/${industryId.toLowerCase()}-banner.jpg`, true);
  };

  const industryList = [
    { id: 'MINERIA', name: 'Minería', image: getIndustryImage('mineria') },
    { id: 'CONSTRUCCION', name: 'Construcción', image: getIndustryImage('construccion') },
    { id: 'MANUFACTURA', name: 'Manufactura', image: getIndustryImage('manufactura') },
    { id: 'PETROLERA', name: 'Petrolera', image: getIndustryImage('petrolera') },
    { id: 'QUIMICA', name: 'Química', image: getIndustryImage('quimica') },
    { id: 'METALMECANICA', name: 'Metalmecánica', image: getIndustryImage('metalmecanica') },
    { id: 'LOGISTICA', name: 'Logística', image: getIndustryImage('logistica') },
    { id: 'SALUD', name: 'Salud', image: getIndustryImage('salud') },
    { id: 'AGRICOLA', name: 'Agrícola', image: getIndustryImage('agricola') },
    { id: 'ALIMENTICIA', name: 'Alimenticia', image: getIndustryImage('alimenticia') }
  ];

  // Obtener los subfilters para la industria seleccionada
  const currentSubfilters = selectedIndustry ? 
    getSubfiltersForIndustry(selectedIndustry) : [];

  // Manejador para cuando se hace clic en un subfiltro
  const handleSubfilterClick = (subfilterName: string) => {
    if (onSubfilterSelect) {
      onSubfilterSelect(subfilterName);
    }
  };

  return (
    <div className="relative w-full overflow-hidden rounded-xl shadow-lg">
      {/* Toggle collapse button - solo visible cuando hay una industria seleccionada */}
      {selectedIndustry && (
        <button 
          onClick={toggleCollapse}
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-50 
                     bg-amber-500 hover:bg-amber-600 text-white 
                     rounded-full p-2 shadow-lg 
                     transition-all duration-300"
          style={{ 
            boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
            position: 'absolute',
            zIndex: 9999,
            bottom: '-0rem'
          }}
        >
          {isCollapsed ? 
            <ChevronDown className="w-5 h-5" /> : 
            <ChevronUp className="w-5 h-5" />
          }
        </button>
      )}

      <AnimatePresence mode="wait">
        {!selectedIndustry ? (
          // Vista de todas las industrias - altura fija
          <motion.div
            key="industries"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0, x: -100 }}
            className="flex overflow-hidden py-4 px-2 bg-gray-100"
          >
            {industryList.map((industry) => (
              <motion.div
                key={industry.id}
                className="relative flex-shrink-0 cursor-pointer group mx-2 flex-1"
                whileHover={{ scale: 1.05 }}
                onClick={() => onIndustrySelect(industry.id)}
              >
                <div className={`relative overflow-hidden rounded-xl shadow-md ${getIndustryCardHeight()}`}>
                  <img
                    src={industry.image}
                    alt={industry.name}
                    className="w-full h-full object-cover object-center"
                    onError={(e) => {
                      // Try alternative formats if jpg fails
                      const currentSrc = e.currentTarget.src;
                      if (currentSrc.endsWith('.jpg')) {
                        e.currentTarget.src = currentSrc.replace('.jpg', '.jpeg');
                      } else if (currentSrc.endsWith('.jpeg')) {
                        e.currentTarget.src = currentSrc.replace('.jpeg', '.svg');
                      } else {
                        // If all formats fail, use default banner
                        e.currentTarget.src = getPublicImageUrl('banners/default-banner.jpeg', true);
                      }
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                  <h3 className="absolute bottom-4 left-4 text-white text-xl font-semibold drop-shadow-md">
                    {industry.name}
                  </h3>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          // Vista detallada de una industria con subfilters
          <motion.div
            key="detail"
            className={`flex flex-col md:flex-row bg-gray-100 rounded-xl transition-all duration-300 ${isCollapsed ? 'max-h-36' : 'max-h-76'}`}
            style={{ 
              height: isCollapsed ? '9rem' : '19rem', // 36px vs 76px en rem
              overflow: 'hidden'
            }}
          >
            {/* Industria seleccionada (izquierda) */}
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className={`w-full md:w-1/3 p-4 transition-all duration-300`}
            >
              <button
                onClick={onBack}
                className="flex items-center text-gray-600 hover:text-amber-500 mb-2"
              >
                <ChevronLeft className="w-5 h-5 mr-2" />
                Volver a industrias
              </button>
              
              <div className={`relative rounded-lg overflow-hidden shadow-md transition-all duration-300`}
                   style={{ height: isCollapsed ? '5rem' : '13rem' }}>
                {industryList.find(i => i.id === selectedIndustry) && (
                  <>
                    <img
                      src={industryList.find(i => i.id === selectedIndustry)?.image}
                      alt={industryList.find(i => i.id === selectedIndustry)?.name}
                      className="w-full h-full object-cover object-center"
                      onError={(e) => {
                        // Use default banner as fallback
                        e.currentTarget.src = getPublicImageUrl('banners/default-banner.jpeg', true);
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <h2 className="absolute bottom-2 left-4 text-white text-xl font-bold">
                      {industryList.find(i => i.id === selectedIndustry)?.name}
                    </h2>
                  </>
                )}
              </div>
            </motion.div>
            
            {/* Subfilters (derecha) */}
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={`w-full md:w-2/3 p-4 transition-all duration-300`}
              style={{ overflow: 'hidden' }}
            >
              <h3 className="text-lg font-semibold mb-2">Equipos de Protección</h3>
              
              <div
                className="grid grid-cols-3 gap-3"
                style={{
                  height: isCollapsed ? '4rem' : '14rem',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease'
                }}
              >
                {currentSubfilters.map((filter) => (
                  <div
                    key={filter.id}
                    className={`relative cursor-pointer group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ${
                      isSubfilterSelected(filter.name) ? 'ring-2 ring-amber-500' : ''
                    }`}
                    style={{
                      height: '100%',
                      aspectRatio: '4/3' // Cambiado de 1/1 a 4/3 para hacerlas más anchas que altas
                    }}
                    onClick={() => handleSubfilterClick(filter.name)}
                  >
                    {isSubfilterSelected(filter.name) && (
                      <div className="absolute top-1 right-1 z-10 bg-amber-500 rounded-full p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                    <div className="relative w-full h-full">
                      <img
                        src={filter.image}
                        alt={filter.name}
                        className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                      <h4 className="absolute bottom-1 left-2 text-white text-sm font-medium">
                        {filter.name}
                      </h4>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default IndustrySelector;
