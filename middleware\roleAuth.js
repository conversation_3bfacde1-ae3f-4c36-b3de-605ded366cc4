import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

export const checkRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      if (!token) {
        return res.status(401).json({ error: 'No token provided' });
      }

      // Verificar el token y obtener el usuario
      const { data: { user }, error: authError } = await supabase.auth.getUser(token);
      if (authError || !user) {
        return res.status(401).json({ error: 'Invalid token' });
      }

      // Obtener el perfil del usuario
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('client_type')
        .eq('id', user.id)
        .single();

      if (profileError || !profile) {
        console.error('Profile error:', profileError);
        return res.status(401).json({ error: 'Profile not found' });
      }

      // Logging para debug
      console.log('Role check:', {
        userType: profile.client_type,
        allowedRoles,
        hasAccess: allowedRoles.includes(profile.client_type)
      });

      // Verificar si el rol del usuario está permitido
      if (!allowedRoles.includes(profile.client_type)) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          userType: profile.client_type,
          requiredRoles: allowedRoles
        });
      }

      // Agregar el perfil al objeto de solicitud
      req.userProfile = profile;
      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
};
