import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '../lib/supabase';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  userProfile: {
    id: string;
    name: string;
    client_type: 'small' | 'medium' | 'large' | 'admin';
  } | null;
  login: (token: string, userData: any) => Promise<void>;
  logout: () => Promise<void>;
  hasRole: (roles: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        throw error;
      }

      console.log('Fetched profile:', data);
      return data;
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      return null;
    }
  };

  const login = async (token: string, userData: any) => {
    try {
      setIsLoading(true);
      console.log('Login initiated for user:', userData);
      
      const profile = await fetchUserProfile(userData.id);
      if (!profile) {
        throw new Error('Failed to fetch user profile');
      }

      console.log('User profile loaded:', profile);

      // Update localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('userProfile', JSON.stringify(profile));

      // Update state
      setUser(userData);
      setUserProfile(profile);
      setIsAuthenticated(true);

      return { success: true, profile };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    await supabase.auth.signOut();
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userProfile');
    setIsAuthenticated(false);
    setUser(null);
    setUserProfile(null);
  };

  const hasRole = (roles: string[]) => {
    return userProfile && roles.includes(userProfile.client_type);
  };

  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          const profile = await fetchUserProfile(session.user.id);
          if (profile) {
            setUser(session.user);
            setUserProfile(profile);
            setIsAuthenticated(true);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      user, 
      userProfile, 
      login, 
      logout,
      hasRole 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Mover la exportación del hook useAuth fuera de cualquier función
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
