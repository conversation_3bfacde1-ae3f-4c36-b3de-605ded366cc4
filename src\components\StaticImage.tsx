import React from 'react';
import { getPublicImageUrl } from '../config/storage';

interface StaticImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  name: string;
  fallback?: string;
}

export const StaticImage: React.FC<StaticImageProps> = ({ 
  name, 
  fallback = 'placeholder-product.jpg',
  alt = '',
  ...props 
}) => {
  const src = getPublicImageUrl(name, true);

  return (
    <img
      src={src}
      alt={alt}
      onError={(e) => {
        e.currentTarget.src = getPublicImageUrl(fallback, true);
      }}
      {...props}
    />
  );
};