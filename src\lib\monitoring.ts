import logger from './logger';

interface PerformanceMetrics {
  pageLoad: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
}

export const monitorPerformance = (): void => {
  // Métricas básicas de rendimiento
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const metrics: PerformanceMetrics = {
      pageLoad: navigation.loadEventEnd - navigation.startTime,
    };

    // Métricas Web Vitals
    const paint = performance.getEntriesByType('paint');
    const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
    if (fcp) {
      metrics.firstContentfulPaint = fcp.startTime;
    }

    logger.info('Page Performance Metrics', metrics);
  });

  // Monitoreo de errores
  window.addEventListener('error', (event) => {
    logger.error('Client Error', {
      message: event.message,
      filename: event.filename,
      lineNumber: event.lineno,
      columnNumber: event.colno,
    });
  });

  // Monitoreo de promesas no manejadas
  window.addEventListener('unhandledrejection', (event) => {
    logger.error('Unhandled Promise Rejection', {
      reason: event.reason,
    });
  });

  // Monitor font loading
  if ('performance' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.name.includes('Inter')) {
          logger.info('Font Performance Metric', {
            name: entry.name,
            duration: entry.duration,
            startTime: entry.startTime
          });
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  // Monitor font loading through document.fonts
  if ('fonts' in document) {
    const startTime = performance.now();
    document.fonts.ready.then(() => {
      const duration = performance.now() - startTime;
      logger.info('Fonts loaded', { duration });
    });
  }
};
