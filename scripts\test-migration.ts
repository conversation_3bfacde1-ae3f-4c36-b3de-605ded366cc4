const testEndpoints = [
  '/api/products',
  '/api/catalog',
  '/api/featured-products'
];

const testMigration = async () => {
  const railwayUrl = 'https://release-v1.up.railway.app/';
  const vercelUrl = 'https://cr-work.vercel.app/';
  
  for (const endpoint of testEndpoints) {
    try {
      const [railwayResponse, vercelResponse] = await Promise.all([
        fetch(`${railwayUrl}${endpoint}`),
        fetch(`${vercelUrl}${endpoint}`)
      ]);
      
      const railwayData = await railwayResponse.json();
      const vercelData = await vercelResponse.json();
      
      console.log(`✅ ${endpoint}: Both responses received`);
      console.log(`Railway: ${railwayData.length} items`);
      console.log(`Vercel: ${vercelData.length} items`);
      
    } catch (error) {
      console.error(`❌ ${endpoint}: Error -`, error);
    }
  }
};