import React, { useState } from 'react';
import { LucideIcon, ArrowRight, ExternalLink } from 'lucide-react';

interface FeatureCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
  onClick: () => void;
  highlight?: boolean;
  compact?: boolean;
  imageUrl?: string;
  category?: string; // Categoría del producto
  disabled?: boolean; // Estado deshabilitado
  badge?: string; // Badge opcional (ej: "Nuevo", "Popular")
  price?: string; // Precio opcional
  features?: string[]; // Lista de características principales
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  onClick,
  highlight = false,
  compact = false,
  imageUrl,
  category,
  disabled = false,
  badge,
  price,
  features = []
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (!disabled) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <article 
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-label={`Ver productos de ${title}`}
      className={`
        group relative overflow-hidden rounded-2xl
        transition-all duration-300 ease-out
        transform hover:-translate-y-1 
        focus:outline-none focus:ring-4 focus:ring-blue-200
        flex flex-col
        ${compact 
          ? 'min-h-64' 
          : 'h-80'}
        ${disabled 
          ? 'opacity-60 cursor-not-allowed' 
          : 'cursor-pointer hover:shadow-2xl'}
        ${highlight 
          ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-2 border-blue-200 shadow-lg' 
          : 'bg-white hover:bg-gray-50 border border-gray-200 shadow-md hover:shadow-xl'}
      `}
    >
      {/* Badge */}
      {badge && (
        <div className="absolute top-4 right-4 z-30">
          <span className={`
            px-3 py-1 rounded-full text-xs font-semibold
            ${highlight 
              ? 'bg-blue-500 text-white' 
              : 'bg-gradient-to-r from-green-400 to-green-500 text-white'}
            shadow-sm
          `}>
            {badge}
          </span>
        </div>
      )}

      {/* Imagen de fondo mejorada */}
      {imageUrl && (
        <div className="absolute inset-0 w-full h-full">
          <img 
            src={imageUrl} 
            alt={title} 
            className={`
              w-full h-full object-cover transition-all duration-500
              ${isHovered ? 'scale-105 opacity-40' : 'opacity-10'}
            `}
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </div>
      )}

      {/* Contenido principal */}
      <div className="relative z-10 flex flex-col h-full p-6">
        {/* Header mejorado */}
        <header className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`
              p-3 rounded-xl transition-all duration-300
              ${highlight 
                ? 'bg-blue-100 group-hover:bg-blue-200' 
                : 'bg-gray-100 group-hover:bg-blue-100'}
              ${isHovered ? 'scale-110' : 'scale-100'}
            `}>
              <Icon className={`
                w-6 h-6 transition-colors duration-300
                ${highlight 
                  ? 'text-blue-600' 
                  : 'text-gray-600 group-hover:text-blue-600'}
              `} />
            </div>
            <div>
              {category && (
                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  {category}
                </span>
              )}
              <h3 className={`
                font-bold text-lg leading-tight
                ${highlight ? 'text-blue-900' : 'text-gray-900'}
                group-hover:text-blue-800 transition-colors duration-300
              `}>
                {title}
              </h3>
            </div>
          </div>
          
          {price && (
            <div className="text-right">
              <span className="text-sm text-gray-500">Desde</span>
              <div className="font-bold text-lg text-gray-900">{price}</div>
            </div>
          )}
        </header>

        {/* Descripción mejorada */}
        <div className="flex-grow mb-4">
          <p className={`
            text-sm leading-relaxed mb-3
            ${highlight ? 'text-blue-800' : 'text-gray-600'}
          `}>
            {description}
          </p>
          
          {/* Lista de características */}
          {features.length > 0 && (
            <ul className="space-y-1">
              {features.slice(0, 3).map((feature, index) => (
                <li key={index} className="flex items-center text-xs text-gray-500">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-2 flex-shrink-0"></span>
                  {feature}
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* CTA mejorado */}
        <footer className="mt-auto">
          <div className={`
            flex items-center justify-between p-3 rounded-xl
            transition-all duration-300
            ${highlight 
              ? 'bg-blue-500 hover:bg-blue-600' 
              : 'bg-gray-900 hover:bg-blue-600'}
            ${disabled ? 'bg-gray-400' : ''}
            group-hover:shadow-lg
          `}>
            <span className="text-white font-medium text-sm">
              {disabled ? 'No disponible' : 'Ver productos'}
            </span>
            <ArrowRight className={`
              w-4 h-4 text-white transition-transform duration-300
              ${isHovered && !disabled ? 'translate-x-1' : ''}
            `} />
          </div>
        </footer>
      </div>

      {/* Indicador de hover */}
      <div className={`
        absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500
        transition-all duration-300 ease-out
        ${isHovered && !disabled ? 'w-full' : 'w-0'}
      `} />
    </article>
  );
};

export default FeatureCard;