{"version": 2, "builds": [{"src": "api/**/*.ts", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1", "headers": {"Access-Control-Allow-Origin": "https://cr-seg-ind.pages.dev", "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS", "Access-Control-Allow-Headers": "Content-Type,Authorization", "Cache-Control": "public, max-age=60, s-maxage=60"}}], "env": {"VITE_SUPABASE_URL": "${VITE_SUPABASE_URL}", "VITE_SUPABASE_ANON_KEY": "${VITE_SUPABASE_ANON_KEY}", "SUPABASE_SERVICE_ROLE_KEY": "${VITE_SUPABASE_SERVICE_KEY}"}}