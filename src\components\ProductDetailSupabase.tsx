import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Product } from '../types';
import { getProductImageUrl } from '../config/storage';

const ProductDetailSupabase = () => {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;

        // Obtener la URL de la imagen usando la función de storage
        const productImageUrl = await getProductImageUrl(id, data.image_url);
        
        setProduct({
          ...data,
          image_url: productImageUrl
        });
        setImageUrl(productImageUrl);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'Error fetching product');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  if (loading) return <div>Loading product details...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!product) return <div>Product not found</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">{product.name}</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <img
            src={product.image_url || '/images/placeholder-product.jpg'}
            alt={product.name}
            className="w-full rounded-lg"
          />
        </div>
        <div>
          <p className="text-xl font-bold mb-2">${product.price}</p>
          <p className="mb-4">{product.description}</p>
          <div className="mb-4">
            <h2 className="font-bold mb-2">Características:</h2>
            <p>{product.caracteristicas}</p>
          </div>
          <div className="mb-4">
            <h2 className="font-bold mb-2">Especificaciones:</h2>
            <p>{product.especificaciones}</p>
          </div>
          <div className="mb-4">
            <h2 className="font-bold mb-2">Presentación:</h2>
            <p>{product.presentacion}</p>
          </div>
          {product.documentacion && (
            <div>
              <h2 className="font-bold mb-2">Documentación:</h2>
              <a
                href={product.documentacion}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Ver documentación
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailSupabase;

