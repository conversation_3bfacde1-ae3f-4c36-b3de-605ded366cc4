import { supabase } from '../src/lib/supabase';

async function migrateImages() {
  try {
    // 1. Obtener todos los productos
    const { data: products, error } = await supabase
      .from('products')
      .select('id, image_url');

    if (error) throw error;

    for (const product of products) {
      if (!product.image_url) continue;

      try {
        // 2. <PERSON><PERSON>gar imagen actual
        const response = await fetch(product.image_url);
        const blob = await response.blob();
        const fileExt = product.image_url.split('.').pop()?.toLowerCase() || 'jpg';
        
        // 3. Subir con nuevo formato de nombre
        const newPath = `${product.id}/main.${fileExt}`;
        await supabase.storage
          .from('product-images')
          .upload(newPath, blob, {
            upsert: true,
            contentType: `image/${fileExt}`
          });

        // 4. Actualizar URL en la base de datos
        const { data: { publicUrl } } = supabase.storage
          .from('product-images')
          .getPublicUrl(newPath);

        await supabase
          .from('products')
          .update({ image_url: publicUrl })
          .eq('id', product.id);

        console.log(`Migrated image for product ${product.id}`);
      } catch (err) {
        console.error(`Error migrating image for product ${product.id}:`, err);
      }
    }
  } catch (error) {
    console.error('Migration error:', error);
  }
}

migrateImages();