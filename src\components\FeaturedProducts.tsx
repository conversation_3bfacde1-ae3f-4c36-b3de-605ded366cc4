import React, { useRef, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { useQuery } from '@tanstack/react-query';
import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as supabaseClientUntyped } from '../lib/supabase';
import { Skeleton } from './ui/Skeleton';
import { ShoppingCart } from 'lucide-react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css'; // Importa los estilos
const supabase: SupabaseClient = supabaseClientUntyped as SupabaseClient;
import { FeaturedProduct, Product } from '../types';
import { IMAGES } from '../config/constants';
import { FEATURE_FLAGS } from '../config/featureFlags';

interface DatabaseFeaturedProduct {
  id: number;
  product_id: string;
  featured_type: string;
  start_date: string;
  end_date: string;
  created_at: string;
  products: Product;
}

interface FeaturedProductsProps {
  onAddToCart: (product: Product, quantity: number) => void;
  isAuthenticated: boolean;
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ onAddToCart, isAuthenticated }) => {
  const navigate = useNavigate();
  const productsRef = useRef<HTMLDivElement>(null);
  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});

  const { data: featuredProducts, isLoading, error } = useQuery<FeaturedProduct[]>({
    queryKey: ['featuredProducts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('featured_products')
        .select(`
          id,
          product_id,
          featured_type,
          start_date,
          end_date,
          created_at,
          products (
            id,
            name,
            price,
            image_url,
            categories,
            brand
          )
        `)
        .eq('active', true)
        .limit(7); // Aumentado de 4 a 5

      if (error) throw error;

      return (data as any[]).map((item: any) => ({
        id: item.id,
        product_id: item.product_id,
        featured_type: item.featured_type,
        start_date: item.start_date,
        end_date: item.end_date,
        created_at: item.created_at,
        product_data: item.products,
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Observador de intersección para animaciones al hacer scroll
  useEffect(() => {
    if (!productsRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const products = productsRef.current?.querySelectorAll('.product-card');
            products?.forEach((product, index) => {
              setTimeout(() => {
                product.classList.add('animate-in');
              }, index * 150);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(productsRef.current);

    return () => {
      observer.disconnect();
    };
  }, [featuredProducts]);

  const handleImageLoad = (productId: string) => {
    setLoadedImages(prev => ({
      ...prev,
      [productId]: true
    }));
  };

  const addToOrder = (e: React.MouseEvent, product: Product) => {
    e.preventDefault();
    e.stopPropagation();
    if (FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated) {
      onAddToCart(product, 1);
      toast.success('Producto añadido al pedido');
    } else {
      toast.error('Debes iniciar sesión para agregar productos al carrito');
    }
  };

  const handleProductClick = (productId: string) => {
    navigate(`/product/${productId}`);
  };

  if (isLoading) {
    return (
      <section className="py-12 bg-gradient-to-b from-white to-gray-100" aria-label="Cargando productos destacados">
        <div className="container mx-auto px-4 max-w-6xl">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-4 shadow-sm">
                <Skeleton className="aspect-square rounded-lg" />
                <Skeleton className="h-4 w-3/4 mt-4" />
                <Skeleton className="h-6 w-1/2 mt-2" />
                <Skeleton className="h-8 w-full mt-4 rounded-full" />
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    console.error('Error loading featured products:', error);
    return (
      <div className="py-8 text-center" role="alert">
        <p className="text-red-600">No se pudieron cargar los productos destacados. Por favor, intente más tarde.</p>
      </div>
    );
  }

  if (!featuredProducts?.length) {
    return null;
  }

  const getBadgeStyles = (type: string) => {
    switch (type) {
      case 'new':
        return 'bg-green-100 text-green-800';
      case 'featured':
        return 'bg-amber-100 text-amber-800';
      case 'sale':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBadgeText = (type: string) => {
    switch (type) {
      case 'new':
        return 'Nuevo';
      case 'top':
        return 'Top';
      default:
        return 'Destacado';
    }
  };

  return (
    <section className="py-8 bg-gray-800"> {/* Cambiado de py-4 a py-6 y color de fondo */}
      <div className="container mx-auto px-4">

        <div 
          ref={productsRef} 
          className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-8 max-w-[85%] mx-auto place-items-center"
          aria-label="Lista de productos destacados"
        >
          {/* Mostramos hasta 5 productos */}
          {featuredProducts?.slice(0, 5).map((featured) => {
            const product = featured.product_data;
            const isImageLoaded = loadedImages[featured.product_id];
            
            return (
              <div 
                key={featured.product_id}
                className="product-card group p-2 rounded-lg w-full" // Increased padding from p-3 to p-4 y se eliminó bg-gray-800
              >
                {/* Contenedor de imagen con badge */}
                <div 
                  className="relative aspect-square mb-2 cursor-pointer"
                  onClick={() => handleProductClick(featured.product_id)}
                  role="link"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      handleProductClick(featured.product_id);
                    }
                  }}
                >
                  <img
                    src={product.image_url || '/placeholder-product.png'}
                    alt={product.name}
                    className={`w-full h-full object-contain transition-transform duration-500 group-hover:scale-105 
                              ${isImageLoaded ? 'opacity-100' : 'opacity-0'}`}
                    loading="lazy"
                    onLoad={() => handleImageLoad(featured.product_id)}
                  />
                  <div className="absolute top-1 left-1"> {/* Ajustado posición del badge */}
                    <Badge className={`px-1.5 py-0.5 text-xs font-semibold rounded-full shadow-sm ${getBadgeStyles(featured.featured_type)}`}>
                      {getBadgeText(featured.featured_type)}
                    </Badge>
                  </div>
                </div>

                {/* Información del producto */}
                <div className="space-y-0.5"> {/* Reducido el espaciado vertical */}
                  <span className="text-xs font-medium text-sky-400">{product.category}</span> {/* Color de categoría ajustado para contraste */}
                  <h3 className="text-sm font-medium text-white line-clamp-1">{product.name}</h3>
                  <p className="text-xs text-gray-300">{product.brand}</p>
                  
                  <Button
                    onClick={(e) => addToOrder(e, product)}
                    className="w-full mt-1 bg-amber-500 hover:bg-amber-600 text-white py-1 px-2 rounded-lg 
                             transition-colors duration-300 flex items-center justify-center gap-1 
                             focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 text-xs"
                    aria-label={`Añadir ${product.name} al carrito`}
                  >
                    <ShoppingCart size={14} />
                    <span>Añadir</span>
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      <style>{`
        .product-card {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
        }
        .product-card.animate-in {
          opacity: 1;
          transform: translateY(0);
        }
      `}</style>
    </section>
  );
};

export default FeaturedProducts;
