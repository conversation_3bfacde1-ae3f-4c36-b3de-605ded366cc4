import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Función para verificar variables de forma segura
const checkVar = (value) => {
  if (!value) return '✗';
  return '✓';
};

console.log('\nEnvironment Variables Check:');
console.log('===========================');
console.log('VITE_SUPABASE_URL:', checkVar(process.env.VITE_SUPABASE_URL));
console.log('VITE_SUPABASE_ANON_KEY:', checkVar(process.env.VITE_SUPABASE_ANON_KEY));
console.log('NODE_ENV:', process.env.NODE_ENV || 'not set');
console.log('PORT:', process.env.PORT || 'not set');

// Verificar si las variables críticas están presentes
const missingVars = [];
if (!process.env.VITE_SUPABASE_URL) missingVars.push('VITE_SUPABASE_URL');
if (!process.env.VITE_SUPABASE_ANON_KEY) missingVars.push('VITE_SUPABASE_ANON_KEY');

if (missingVars.length > 0) {
  console.error('\n⚠️  Missing critical environment variables:');
  missingVars.forEach(variable => console.error(`   - ${variable}`));
  console.error('\nPlease check your .env file and ensure all required variables are set.');
  process.exit(1);
} else {
  console.log('\n✅ All critical environment variables are set.');
}
