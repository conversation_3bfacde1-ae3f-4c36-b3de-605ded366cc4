import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Navigate } from 'react-router-dom';
import AdminLayout from '../components/admin/AdminLayout';

const Dashboard = React.lazy(() => import('../components/admin/Dashboard'));
const Products = React.lazy(() => import('../components/admin/Products'));
const Users = React.lazy(() => import('../components/admin/Users'));
const Orders = React.lazy(() => import('../components/admin/Orders'));

const AdminRoutes = () => {
  const { userProfile } = useAuth();

  console.log('AdminRoutes - Current user profile:', userProfile);

  if (!userProfile || userProfile.client_type !== 'admin') {
    console.log('Access denied to admin panel:', userProfile);
    return <Navigate to="/unauthorized" replace />;
  }

  return (
    <AdminLayout>
      <React.Suspense fallback={<div>Cargando...</div>}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/products" element={<Products />} />
          <Route path="/users" element={<Users />} />
          <Route path="/orders" element={<Orders />} />
        </Routes>
      </React.Suspense>
    </AdminLayout>
  );
};

export default AdminRoutes;

