import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { LayoutDashboard, Package, Users, ShoppingCart } from 'lucide-react';

const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();

  const menuItems = [
    { path: '/admin', icon: LayoutDashboard, label: 'Dashboard' },
    { path: '/admin/products', icon: Package, label: 'Productos' },
    { path: '/admin/users', icon: Users, label: 'Usuarios' },
    { path: '/admin/orders', icon: ShoppingCart, label: 'Pedidos' },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar - Agregamos fixed y ajustamos z-index */}
      <div className="fixed left-0 top-0 h-screen w-64 bg-black text-amber-300 p-4 z-20">
        <h2 className="text-xl font-bold mb-6">Panel Admin</h2>
        <nav>
          {menuItems.map(({ path, icon: Icon, label }) => (
            <Link
              key={path}
              to={path}
              className={`
                flex items-center space-x-2 p-3 rounded-lg mb-2
                ${isActive(path) 
                  ? 'bg-amber-300/10 text-amber-300' 
                  : 'hover:bg-amber-300/5'
                }
              `}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </Link>
          ))}
        </nav>
      </div>

      {/* Main content - Agregamos margin-left para compensar el sidebar fijo */}
      <div className="flex-1 ml-64 p-8">
        {children}
      </div>
    </div>
  );
};

export default AdminLayout;
