import React, { useState, useEffect } from 'react';
import { ChevronUp } from 'lucide-react';

interface ScrollToTopButtonProps {
  isCartOpen: boolean;
}

const ScrollToTopButton: React.FC<ScrollToTopButtonProps> = ({ isCartOpen }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="fixed bottom-32 right-6 z-50">
      <button
        onClick={scrollToTop}
        className={`
          group bg-black/80 hover:bg-amber-600 text-white 
          p-2.5 rounded-full shadow-lg 
          transition-all duration-300 
          flex items-center justify-center 
          backdrop-blur-sm border border-amber-500/20
          ${isVisible && !isCartOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}
        `}
        aria-label="Ir arriba"
      >
        <ChevronUp className="w-4 h-4" />
      </button>
    </div>
  );
};

export default ScrollToTopButton;

