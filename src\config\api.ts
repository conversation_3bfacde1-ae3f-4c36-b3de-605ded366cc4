// Configuración para backend en Vercel
const API_CONFIG = {
  VERCEL_BASE_URL: 'https://cr-work.vercel.app'
};

export const getApiBaseUrl = (): string => {
  // En desarrollo, usar localhost si está disponible
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3010';
  }
  return API_CONFIG.VERCEL_BASE_URL;
};

// Función helper para requests
export const apiRequest = async (endpoint: string, options?: RequestInit) => {
  const baseUrl = getApiBaseUrl();
  const url = `${baseUrl}${endpoint}`;
  
  console.log(`API Request: ${url}`); // Para debugging
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers
    }
  });
  
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};
