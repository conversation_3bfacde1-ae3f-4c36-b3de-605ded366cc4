-- Crear el schema maintenance si no existe
create schema if not exists maintenance;

-- Tabla para health checks
create table if not exists public.health_check (
    id uuid default gen_random_uuid() primary key,
    last_check timestamp with time zone default now(),
    status text not null,
    details jsonb
);

-- Tabla para logs de API
create table if not exists public.api_logs (
    id uuid default gen_random_uuid() primary key,
    timestamp timestamp with time zone default now(),
    method text,
    path text,
    user_id uuid references auth.users(id),
    status integer,
    duration integer,
    ip_address text,
    user_agent text
);

-- Índices para búsqueda eficiente
create index if not exists idx_api_logs_timestamp on public.api_logs(timestamp);
create index if not exists idx_api_logs_user_id on public.api_logs(user_id);
create index if not exists idx_api_logs_status on public.api_logs(status);

-- RLS policies
alter table public.health_check enable row level security;
alter table public.api_logs enable row level security;

-- Políticas para health_check
do $$
begin
    if not exists (
        select 1 from pg_policies 
        where tablename = 'health_check' and policyname = 'Health check visible to authenticated users only'
    ) then
        create policy "Health check visible to authenticated users only"
            on public.health_check for select
            to authenticated
            using (true);
    end if;
end
$$;

-- Políticas para api_logs
do $$
begin
    if not exists (
        select 1 from pg_policies 
        where tablename = 'api_logs' and policyname = 'API logs visible to authenticated users only'
    ) then
        create policy "API logs visible to authenticated users only"
            on public.api_logs for select
            to authenticated
            using (true);
    end if;
end
$$;

-- Función para limpiar logs antiguos (retiene 30 días)
create or replace function maintenance.cleanup_old_logs()
returns void as $$
begin
    delete from public.api_logs
    where timestamp < now() - interval '30 days';
end;
$$ language plpgsql security definer;

-- Verificar si pg_cron está instalado
create extension if not exists pg_cron;

-- Programar limpieza diaria
select cron.schedule(
    'cleanup-old-logs',
    '0 0 * * *',  -- Ejecutar a medianoche
    $$select maintenance.cleanup_old_logs()$$
);
