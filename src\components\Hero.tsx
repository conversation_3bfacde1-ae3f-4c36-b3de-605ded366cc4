import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { IMAGES } from '../config/constants';

const Hero = () => {
  // Configuración de animaciones
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="relative min-h-screen bg-cover bg-center bg-fixed flex items-center">
      {/* Background con efecto parallax */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-fixed"
        style={{
          backgroundImage: `url('${IMAGES.HERO_BACKGROUND}')`
        }}
      />

      {/* Overlay mejorado con múltiples capas */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/30 to-black/30" />
        <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px]" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/40 via-transparent to-black/40" />
        {/* Degradado inferior */}
        <div className="absolute bottom-0 left-0 right-0 h-[200px] bg-gradient-to-t from-black via-black/70 to-transparent" />
      </div>

      {/* Agregar un nuevo div para el degradado inferior */}
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-gray-900 to-transparent z-10" />

      {/* Contenido principal */}
      <motion.div 
        className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="max-w-4xl">
          {/* Título principal */}
          <motion.div variants={itemVariants} className="space-y-2">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
              Seguridad Industrial y{' '}
              <br />
              <span className="hero-gradient-text">
                Equipos de Protección
              </span>
            </h1>
          </motion.div>

          {/* Subtítulo */}
          <motion.p 
            variants={itemVariants}
            className="text-xl md:text-2xl text-gray-300 mt-6 mb-8 max-w-2xl leading-relaxed font-light"
          >
            Protegemos lo más valioso de tu empresa: tu gente. 
            Equipos de seguridad industrial de primera calidad.
          </motion.p>

          {/* Contenedor de CTAs */}
          <motion.div 
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-5"
          >
            {/* CTA Primario */}
            <Link
              to="/catalog"
              className="relative inline-flex items-center justify-center 
                         px-6 py-3 text-base font-semibold rounded-xl
                         text-white
                         bg-gradient-to-r from-amber-500 to-amber-600
                         hover:from-amber-600 hover:to-amber-700
                         transform transition-all duration-500
                         hover:scale-[1.02] hover:shadow-xl hover:shadow-amber-500/20
                         active:scale-100 
                         focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 focus:ring-offset-black
                         sm:min-w-[170px]"
            >
              Ver Catálogo
              <ArrowRight className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
            
            {/* CTA Secundario */}
            <Link
              to="mailto:<EMAIL>"
              className="relative inline-flex items-center justify-center 
                         px-6 py-3 text-base font-semibold rounded-xl
                         text-amber-500 
                         border-2 border-amber-500
                         hover:bg-amber-500 hover:text-white
                         transform transition-all duration-500
                         hover:scale-[1.02] hover:shadow-xl hover:shadow-amber-500/20
                         active:scale-100
                         focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 focus:ring-offset-black
                         sm:min-w-[170px]"
            >
              Contactar Ventas
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default Hero
