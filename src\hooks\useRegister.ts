import { useState } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';

interface RegisterData {
  email: string;
  password: string;
  name: string;
  client_type: 'small' | 'medium' | 'large';
}

export const useRegister = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { login } = useAuth();

  const register = async (data: RegisterData) => {
    setLoading(true);
    setError(null);

    try {
      // 1. Registrar usuario en Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
      });

      if (authError) throw authError;

      // 2. Crear perfil en la tabla profiles
      const { error: profileError } = await supabase
        .from('profiles')
        .insert([
          {
            id: authData.user?.id,
            name: data.name,
            client_type: data.client_type,
          }
        ]);

      if (profileError) throw profileError;

      // 3. Iniciar sesión automáticamente
      if (authData.session) {
        await login(authData.session.access_token, authData.user);
      }

      return { success: true };
    } catch (err) {
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  return { register, loading, error };
};
