// Estructura unificada para subfiltros por industria
export interface SubfilterItem {
  id: string;
  name: string;
  image: string;
}

// Definición centralizada de subfiltros por industria
export const industrySubfilters: Record<string, SubfilterItem[]> = {
  'MINERIA': [
    { id: 'cascos', name: '<PERSON><PERSON><PERSON>', image: '/images/epp_casco.jpg' },
    { id: 'guantes', name: 'Guantes', image: '/images/epp_guantes.jpg' },
    { id: 'calzado', name: '<PERSON><PERSON><PERSON>', image: '/images/epp_botas.jpg' },
  ],
  'CONSTRUCCION': [
    { id: 'cascos', name: '<PERSON><PERSON><PERSON>', image: '/images/helmet.jpg' },
    { id: 'arnes', name: '<PERSON><PERSON><PERSON>', image: '/images/epp_arnes.jpg' },
    { id: 'chalecos', name: '<PERSON><PERSON><PERSON>', image: '/images/epp_chaleco.jpg' },
    { id: 'guantes', name: 'Guant<PERSON>', image: '/images/epp_guantes.jpg' },
    { id: 'calzado', name: '<PERSON><PERSON><PERSON>', image: '/images/epp_botas.jpg' },
    { id: 'anteojos', name: 'Anteojos', image: '/images/epp_anteojos.jpg' },
  ],
  'MANUFACTURA': [
    { id: 'guantes', name: 'Guantes', image: '/images/epp_guantes.jpg' },
    { id: 'mascarillas', name: 'Mascarillas', image: '/images/epp_mascarilla.jpg' },
    { id: 'proteccion-auditiva', name: 'Protección auditiva', image: '/images/epp_auditiva.jpg' },
  ],
  'PETROLERA': [
    { id: 'trajes-ignifugos', name: 'Trajes ignífugos', image: '/images/epp_ignifugo.jpg' },
    { id: 'respiradores', name: 'Respiradores', image: '/images/epp_respirador.jpg' },
    { id: 'cascos', name: 'Cascos', image: '/images/epp_casco.jpg' },
  ],
  'QUIMICA': [
    { id: 'trajes-quimicos', name: 'Trajes químicos', image: '/images/epp_quimico.jpg' },
    { id: 'respiradores', name: 'Respiradores', image: '/images/epp_respirador.jpg' },
    { id: 'guantes-quimicos', name: 'Guantes químicos', image: '/images/epp_guantes_quimicos.jpg' },
  ],
  'METALMECANICA': [
    { id: 'proteccion-facial', name: 'Protección facial', image: '/images/epp_facial.jpg' },
    { id: 'guantes', name: 'Guantes', image: '/images/epp_guantes.jpg' },
    { id: 'delantales', name: 'Delantales', image: '/images/epp_delantal.jpg' },
  ],
  'LOGISTICA': [
    { id: 'fajas', name: 'Fajas', image: '/images/epp_faja.jpg' },
    { id: 'guantes', name: 'Guantes', image: '/images/epp_guantes.jpg' },
    { id: 'calzado', name: 'Calzado de seguridad', image: '/images/epp_botas.jpg' },
  ],
};

// Función auxiliar para obtener solo los nombres de los subfiltros (para el componente de filtro)
export const getSubfilterNames = (industryNames: string[]): string[] => {
  if (industryNames.length === 0) return [];
  
  // Unir todos los subfiltros de las industrias seleccionadas
  const allSubfilters = industryNames.flatMap(industry => 
    industrySubfilters[industry]?.map(item => item.name) || []
  );
  
  // Eliminar duplicados
  return Array.from(new Set(allSubfilters));
};

// Función auxiliar para obtener los subfiltros completos para una industria (para el banner)
export const getSubfiltersForIndustry = (industry: string): SubfilterItem[] => {
  return industrySubfilters[industry] || [];
};