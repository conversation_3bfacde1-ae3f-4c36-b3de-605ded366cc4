/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar,
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar,
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Scale animation for hover */
.scale-102 {
  transform: scale(1.02);
}

/* Sticky positioning for the worker image */
/* Removed sticky positioning to align with product panel */
@media (min-width: 768px) {
  .sticky-worker-image {
    position: relative;
    align-self: stretch;
    display: flex;
    align-items: center;
  }
}

/* Category icons grid */
.category-icons-grid {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  justify-content: center;
}

@media (max-width: 768px) {
  .category-icons-grid {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Category icon button */
.category-icon-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.6rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  min-width: 80px; /* Increased by ~15% from 70px */
  flex: 0 0 auto;
}

.category-icon-button:hover {
  transform: translateY(-3px);
}

/* Category icon container */
.category-icon-container {
  width: 2.3rem; /* Increased by ~15% from 2rem */
  height: 2.3rem; /* Increased by ~15% from 2rem */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 0.3rem;
}

/* Category icon */
.category-icon {
  width: 1.7rem; /* Increased by ~15% from 1.5rem */
  height: 1.7rem; /* Increased by ~15% from 1.5rem */
}

/* Subcategory card hover effect */
.subcategory-card {
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
  overflow: hidden;
}

.subcategory-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Worker image container */
.worker-image-container {
  position: relative;
  background-color: #1e293b;
  border-radius: 0.75rem;
  padding: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
  height: 100%;
  min-height: 100%;
}

/* Worker image */
.worker-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
  height: 100%;
  width: 100%;
  display: block;
}

/* "Ver Todos los Productos" button hover effect */
.view-all-button {
  transition: all 0.3s ease;
}

.view-all-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.4), 0 4px 6px -2px rgba(245, 158, 11, 0.1);
}

/* Fixed panel height for consistent layout */
.fixed-panel-height {
  height: 600px; /* Fixed height based on indumentaria layout (2 columns x 4 rows + header + button) */
  min-height: 600px;
}

@media (max-width: 768px) {
  .fixed-panel-height {
    height: auto; /* Allow natural height on mobile */
    min-height: auto;
  }
}

/* Subcategories grid with consistent spacing */
.subcategories-grid {
  min-height: 400px; /* Reserve space for up to 4 rows of items */
  align-content: start; /* Align items to the top */
}

@media (min-width: 640px) {
  .subcategories-grid {
    grid-template-rows: repeat(4, minmax(80px, auto)); /* 4 rows with minimum height */
  }
}

@media (max-width: 639px) {
  .subcategories-grid {
    min-height: auto; /* Allow natural height on mobile */
  }
}
