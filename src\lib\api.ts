import { useQuery, QueryClient } from 'react-query';
import { getApiBaseUrl } from '../config/api';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      retry: 1,
      refetchOnWindowFocus: false
    }
  }
});

// Función genérica para fetchear datos
const fetchData = async (endpoint: string) => {
  const baseUrl = getApiBaseUrl();
  const response = await fetch(`${baseUrl}${endpoint}`);
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }
  
  return response.json();
};

// Hooks para diferentes recursos
export const useProducts = () => {
  return useQuery('products', () => fetchData('/api/products'), {
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
};

export const useCatalog = (filters = {}) => {
  const queryParams = new URLSearchParams(filters as Record<string, string>).toString();
  const endpoint = `/api/catalog${queryParams ? `?${queryParams}` : ''}`;
  
  return useQuery(['catalog', filters], () => fetchData(endpoint), {
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
};

export const useHealth = () => {
  return useQuery('health', () => fetchData('/api/health'), {
    staleTime: 60 * 1000, // 1 minuto
    refetchInterval: 60 * 1000 // Refetch cada minuto
  });
};