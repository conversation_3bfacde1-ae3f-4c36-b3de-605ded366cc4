.catalog-container {
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 1rem;
}

@media (min-width: 768px) {
  .catalog-container {
    flex-direction: row;
    margin: 20px;
  }
}

.sidebar {
  width: 100%;
  padding: 1rem;
  background-color: #f4f4f4;
  border-bottom: 2px solid #ccc;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .sidebar {
    width: 250px;
    border-right: 2px solid #ccc;
    border-bottom: none;
    margin-bottom: 0;
  }
}

.sidebar h2 {
  font-size: 1.5em;
  margin-bottom: 10px;
}

.sidebar ul {
  list-style-type: none;
  padding: 0;
}

.sidebar li {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sidebar li:hover {
  background-color: #ddd;
}

/* Comentado para evitar conflictos con Tailwind CSS en ProductCard */
/*
.product-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

@media (min-width: 640px) {
  .product-list {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .product-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
  }
}

.product-card {
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  position: relative;
  transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.product-card img {
  max-width: 100%;
  height: auto;
}
*/

.sticker {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #28a745;
  color: white;
  padding: 5px;
  border-radius: 3px;
  font-size: 0.8em;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Agregar estos estilos para el buscador */
.catalog-search {
  transition: all 0.3s ease;
}

.catalog-search:focus-within {
  transform: scale(1.01);
}

/* Asegurar que el buscador tenga el z-index correcto */
.catalog-search-container {
  z-index: 30;
}

/* Ajustar el espaciado para móviles */
@media (max-width: 768px) {
  .catalog-search-container {
    padding: 0.5rem;
  }
  
  .catalog-search input {
    font-size: 0.875rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
