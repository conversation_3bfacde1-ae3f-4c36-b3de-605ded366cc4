import React, { useState, useRef, useEffect } from 'react';
import { X, ShoppingCart, Search, Menu, ChevronDown, User, LogOut } from 'lucide-react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { industries } from '../utils';
import { useAuth } from '../context/AuthContext';
import { getPublicImageUrl } from '../config/storage';
import './Navbar.css';
import LoginForm from './LoginForm';
import UserRegistration from './UserRegistration';

interface NavbarProps {
    onCartClick: () => void;
    cartItemsCount: number;
}

const Navbar: React.FC<NavbarProps> = ({
    onCartClick,
    cartItemsCount,
}) => {
    const { isAuthenticated, logout, userProfile } = useAuth();
    
    // Añadir logs para depuración
    useEffect(() => {
        console.log('Auth State:', {
            isAuthenticated,
            userProfile
        });
    }, [isAuthenticated, userProfile]);

    const [showLogin, setShowLogin] = useState(false);
    const [isRegistering, setIsRegistering] = useState(false);
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showSearch, setShowSearch] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [showProductsSearch, setShowProductsSearch] = useState(false);
    const [showIndustriesMenu, setShowIndustriesMenu] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isCartOpen, setIsCartOpen] = useState(false);
    const navigate = useNavigate();
    const searchInputRef = useRef<HTMLInputElement>(null);
    const productsSearchRef = useRef<HTMLDivElement>(null);
    const menuRef = useRef<HTMLDivElement>(null);
    const userMenuRef = useRef<HTMLDivElement>(null);
    const location = useLocation();

    // Cerrar el modal de login cuando el usuario se autentica
    useEffect(() => {
        if (isAuthenticated) {
            setShowLogin(false);
        }
    }, [isAuthenticated]);

    const handleLoginClick = () => {
        setShowLogin(true);
    };

    const handleCloseLogin = () => {
        setShowLogin(false);
    };

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                productsSearchRef.current && 
                !productsSearchRef.current.contains(event.target as Node)
            ) {
                setShowProductsSearch(false);
            }
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsMenuOpen(false);
                setShowIndustriesMenu(false);
                setShowSearch(false);
            }
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setShowUserMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSearchButtonClick = () => {
        setShowSearch(true);
        setTimeout(() => {
            searchInputRef.current?.focus();
        }, 50);
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchTerm.trim()) {
            navigate(`/catalog?search=${encodeURIComponent(searchTerm.trim())}`);
            setShowSearch(false);
            setSearchTerm('');
        }
    };

    const handleSearchKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            setShowSearch(false);
        }
    };

    const handleIndustryClick = (industryName: string) => {
        navigate(`/catalog?industry=${encodeURIComponent(industryName)}`);
        setShowIndustriesMenu(false);
    };

    const handleProductSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchTerm.trim()) {
            navigate(`/catalog?search=${encodeURIComponent(searchTerm.trim())}`);
            setShowProductsSearch(false);
            setSearchTerm('');
        }
    };

    const handleLogout = async () => {
        try {
            await logout();
            setShowUserMenu(false);
        } catch (error) {
            console.error('Error al cerrar sesión:', error);
        }
    };

    const handleSwitchToRegister = () => {
        setIsRegistering(true);
    };

    const handleSwitchToLogin = () => {
        setIsRegistering(false);
    };

    const renderAuthButtons = () => {
        if (isAuthenticated && userProfile) {
            return (
                <div className="relative" ref={userMenuRef}>
                    <button
                        onClick={() => setShowUserMenu(!showUserMenu)}
                        className="flex items-center space-x-2 text-amber-300 hover:text-amber-400"
                    >
                        <User className="h-5 w-5" />
                        <span>{userProfile?.name || 'Usuario'}</span>
                        <ChevronDown className="h-4 w-4" />
                    </button>
                    
                    {showUserMenu && (
                        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-black border border-amber-300/20 z-50">
                            <div className="py-1">
                                <Link
                                    to="/perfil"
                                    className="block px-4 py-2 text-sm text-amber-300 hover:bg-amber-300/10"
                                    onClick={() => setShowUserMenu(false)}
                                >
                                    Mi Perfil
                                </Link>
                                {userProfile.client_type === 'admin' && (
                                    <Link
                                        to="/admin"
                                        className="block px-4 py-2 text-sm text-amber-300 hover:bg-amber-300/10"
                                        onClick={() => setShowUserMenu(false)}
                                    >
                                        Panel Admin
                                    </Link>
                                )}
                                <button
                                    onClick={handleLogout}
                                    className="w-full text-left px-4 py-2 text-sm text-amber-300 hover:bg-amber-300/10"
                                >
                                    <div className="flex items-center">
                                        <LogOut className="h-4 w-4 mr-2" />
                                        Salir
                                    </div>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        return (
            <button
                onClick={handleLoginClick}
                className="action-button px-4 py-2 text-white hover:text-white font-semibold text-base"
            >
                Ingreso
            </button>
        );
    };

    const renderMobileAuthButtons = () => {
        if (isAuthenticated && userProfile) {
            return (
                <>
                    <Link 
                        to="/perfil" 
                        className="block text-lg text-amber-300 hover:text-amber-400 border-b border-amber-300/20 pb-4"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Mi Perfil
                    </Link>
                    {userProfile.client_type === 'admin' && (
                        <Link 
                            to="/admin" 
                            className="block text-lg text-amber-300 hover:text-amber-400 border-b border-amber-300/20 pb-4"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Panel Admin
                        </Link>
                    )}
                    <button
                        onClick={handleLogout}
                        className="w-full text-left text-lg text-amber-300 hover:text-amber-400 flex items-center"
                    >
                        <LogOut className="h-5 w-5 mr-2" />
                        Salir
                    </button>
                </>
            );
        }

        return (
            <button
                onClick={() => {
                    handleLoginClick();
                    setIsMenuOpen(false);
                }}
                className="w-full text-left text-lg text-amber-300 hover:text-amber-400"
            >
                Iniciar Sesión
            </button>
        );
    };

    return (
        <>
            <header className="navbar-container">
                <nav>
                    <div className="container mx-auto px-4">
                        <div className="flex justify-between items-center h-12 md:h-14"> {/* Altura reducida */}
                            {/* Logo */}
                            <div className="flex-shrink-0 relative">
                                <span className="absolute -left-12 top-1/2 -translate-y-1/2 text-gray-500 stars-container" aria-label="Tres estrellas campeonato mundial">
                                    <span className="stars-size">★</span>
                                    <span className="relative -top-1 stars-size">★</span>
                                    <span className="stars-size">★</span>
                                </span>
                                <Link 
                                    to="/" 
                                    className="flex items-center"
                                    onClick={() => {
                                        setIsMenuOpen(false);
                                        navigate('/');
                                        window.scrollTo({
                                            top: 0,
                                            behavior: 'smooth'
                                        });
                                    }}
                                >
                                    <div className="bg-white rounded-full p-1 flex items-center justify-center">
                                        <img
                                            src={getPublicImageUrl('cr-work-logo.png', true)}
                                            alt="CR Work Logo"
                                            className="h-9 w-9 md:h-12 md:w-12 object-contain rounded-full"
                                        />
                                    </div>
                                    <img
                                        src={getPublicImageUrl('title_nobkg.png', true)}
                                        alt="CR Work"
                                        className="ml-6 h-11 md:h-11"
                                    />
                                </Link>
                            </div>

                            {/* Menú móvil */}
                            <div className="flex items-center space-x-2 md:hidden">
                                <button
                                    onClick={onCartClick}
                                    className="cart-button p-2 text-amber-300 hover:text-amber-400 relative"
                                >
                                    <ShoppingCart className="nav-icon h-5 w-5" />
                                    {cartItemsCount > 0 && (
                                        <span className="cart-badge">{cartItemsCount}</span>
                                    )}
                                </button>
                                <button
                                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                                    className="action-button p-2 text-amber-300 hover:text-amber-400"
                                >
                                    <Menu className="nav-icon h-5 w-5" />
                                </button>
                            </div>

                            {/* Menú desktop */}
                            <div className="hidden md:flex md:items-center md:space-x-6">
                                {/* Removed "Inicio" Link */}
                                {/* Menú desplegable de Industrias */}
                                <div 
                                    className="relative group"
                                    onMouseEnter={() => setShowIndustriesMenu(true)}
                                    onMouseLeave={() => setShowIndustriesMenu(false)}
                                >
                                    <button 
                                        className="nav-link flex items-center py-2"
                                    >
                                        <span>Industrias</span>
                                        <ChevronDown className="nav-icon w-4 h-4 ml-1" />
                                    </button>
                                    <div 
                                        className={`dropdown-menu ${
                                            showIndustriesMenu ? 'block' : 'hidden'
                                        }`}
                                    >
                                        <div className="py-2 max-h-96 overflow-y-auto">
                                            {industries.map((industry) => (
                                                <button
                                                    key={industry.name}
                                                    onClick={() => handleIndustryClick(industry.name)}
                                                    className="dropdown-item"
                                                >
                                                    {industry.name}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                                <div 
                                    className="relative group"
                                    onMouseEnter={() => setShowProductsSearch(true)}
                                    onMouseLeave={() => setShowProductsSearch(false)}
                                >
                                    <Link 
                                        to="/catalog"
                                        className="nav-link"
                                    >
                                        Productos
                                    </Link>
                                    
                                    {/* Dropdown de búsqueda de productos */}
                                    {showProductsSearch && (
                                        <div 
                                            ref={productsSearchRef}
                                            className="dropdown-menu"
                                        >
                                            <form onSubmit={handleProductSearch}>
                                                <div className="relative">
                                                    <Search 
                                                        className="nav-icon absolute left-3 top-1/2 -translate-y-1/2 text-amber-300 w-4 h-4 rotate-0" 
                                                        strokeWidth={2.5}
                                                    />
                                                    <input
                                                        type="text"
                                                        value={searchTerm}
                                                        onChange={(e) => setSearchTerm(e.target.value)}
                                                        placeholder="Buscar productos..."
                                                        className="w-full pl-10 pr-4 py-2 bg-gray-800 text-white rounded-lg 
                                                                 border border-amber-300 focus:outline-none focus:ring-2 
                                                                 focus:ring-amber-300 placeholder-gray-400"
                                                        autoFocus
                                                    />
                                                </div>
                                            </form>
                                        </div>
                                    )}
                                </div>
                                <Link to="/nosotros" className="nav-link">
                                    Nosotros
                                </Link>
                                {/* Eliminar el link a Ubicanos */}
                            </div>

                            {/* Botones de acción - Derecha */}
                            <div className="hidden md:flex items-center space-x-4">
                                {renderAuthButtons()}
                                <button
                                    onClick={onCartClick}
                                    className="cart-button p-2 text-amber-300 hover:text-amber-400 relative"
                                >
                                    <ShoppingCart className="nav-icon h-5 w-5" />
                                    {cartItemsCount > 0 && (
                                        <span className="cart-badge">{cartItemsCount}</span>
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Menú móvil desplegable */}
                        {isMenuOpen && (
                            <div className="fixed inset-0 z-[1001] md:hidden">
                                <div 
                                    className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
                                    onClick={() => setIsMenuOpen(false)}
                                />
                                <div 
                                    ref={menuRef}
                                    className="absolute right-0 top-0 h-full w-72 bg-black transform transition-transform duration-300 ease-out"
                                >
                                    <div className="flex flex-col p-6">
                                        <button
                                            onClick={() => setIsMenuOpen(false)}
                                            className="self-end text-amber-300 hover:text-amber-400 mb-6"
                                        >
                                            <X className="h-6 w-6" />
                                        </button>
                                        
                                        <div className="space-y-6">
                                            {/* Removed "Inicio" Link */}
                                            
                                            <div className="border-b border-amber-300/20 pb-4">
                                                <button 
                                                    onClick={() => setShowIndustriesMenu(!showIndustriesMenu)}
                                                    className="flex items-center justify-between w-full text-lg text-amber-300 hover:text-amber-400"
                                                >
                                                    <span>Industrias</span>
                                                    <ChevronDown className={`w-5 h-5 transition-transform duration-200 ${showIndustriesMenu ? 'rotate-180' : ''}`} />
                                                </button>
                                                
                                                {showIndustriesMenu && (
                                                    <div className="mt-4 pl-4 space-y-3">
                                                        {industries.map((industry) => (
                                                            <button
                                                                key={industry.name}
                                                                onClick={() => {
                                                                    handleIndustryClick(industry.name);
                                                                    setIsMenuOpen(false);
                                                                }}
                                                                className="block w-full text-left text-amber-300/80 hover:text-amber-400 text-base"
                                                            >
                                                                {industry.name}
                                                            </button>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>

                                            <Link 
                                                to="/catalog" 
                                                className="block text-lg text-amber-300 hover:text-amber-400 border-b border-amber-300/20 pb-4"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                Productos
                                            </Link>
                                            
                                            <Link 
                                                to="/nosotros" 
                                                className="block text-lg text-amber-300 hover:text-amber-400 border-b border-amber-300/20 pb-4"
                                                onClick={() => setIsMenuOpen(false)}
                                            >
                                                Nosotros
                                            </Link>
                                            
                                            {/* Eliminar el link a Ubicanos */}
                                            
                                            {renderMobileAuthButtons()}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </nav>

                {/* Search Bar - Floating below navbar */}
                <div 
                    className={`absolute left-0 right-0 transform transition-all duration-300 ${
                        showSearch ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2 pointer-events-none'
                    }`}
                    style={{
                        top: '64px', // Para móvil
                        zIndex: 40
                    }}
                >
                    <div className="container mx-auto px-4">
                        <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
                            <div className="bg-black border border-amber-300 rounded-xl shadow-lg p-3">
                                <input
                                    ref={searchInputRef}
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyDown={handleSearchKeyDown}
                                    placeholder="Buscar productos..."
                                    className="w-full pl-10 pr-10 py-2 bg-gray-800 text-white rounded-xl border border-amber-300 focus:outline-none focus:ring-2 focus:ring-amber-300 transition-all duration-300"
                                />
                                <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-amber-300 w-5 h-5" />
                                <button
                                    type="button"
                                    onClick={() => setShowSearch(false)}
                                    className="absolute right-6 top-1/2 transform -translate-y-1/2 text-amber-300 hover:text-amber-400"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {/* Añadir media query para ajustar la posición en desktop */}
                <style>{`
                    @media (min-width: 768px) {
                        div[style*="top: '64px'"] {
                            top: 80px !important;
                        }
                    }
                `}</style>
            </header>
            <div className="navbar-spacer" /> {/* Espaciador para compensar el navbar fijo */}

            {/* Modal de Autenticación */}
            {showLogin && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 relative">
                        <button
                            onClick={() => {
                                setShowLogin(false);
                                setIsRegistering(false);
                            }}
                            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                        >
                            <X className="w-6 h-6" />
                        </button>

                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-900">
                                {isRegistering ? 'Crear cuenta' : 'Iniciar Sesión'}
                            </h2>
                            <p className="mt-2 text-sm text-gray-600">
                                {isRegistering 
                                    ? 'Completa tus datos para crear una cuenta'
                                    : 'Ingresa tus credenciales para continuar'
                                }
                            </p>
                        </div>

                        {isRegistering ? (
                            <UserRegistration 
                                onSwitchToLogin={handleSwitchToLogin}
                                setShowLogin={setShowLogin}
                            />
                        ) : (
                            <LoginForm 
                                setShowLogin={setShowLogin}
                                onSwitchToRegister={handleSwitchToRegister}
                            />
                        )}
                    </div>
                </div>
            )}
        </>
    );
};

export default Navbar;
