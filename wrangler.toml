name = "CR-Work"
compatibility_date = "2024-01-01"

[site]
bucket = "./dist"

[build]
command = "npm install && npm run build"
output_dir = "dist"
cwd = "."

# Configuración para la rama main (producción)
[env.main]
vars = { 
  NODE_ENV = "production",
  VITE_API_URL = "https://cr-work.vercel.app"
}

[env.development]
vars = { 
  NODE_ENV = "development",
  VITE_API_URL = "http://localhost:3010"
}

[env.release]
vars = { 
  NODE_ENV = "release",
  VITE_API_URL = "https://c6d702b0.cr-work.pages.dev/"
}
