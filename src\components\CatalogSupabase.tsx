import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import type { CatalogProduct } from '../types/catalog';
// Asumimos que RawProductData está exportada desde catalogUtils o la definimos aquí temporalmente
// import { transformRawProductToCatalogProduct, RawProductData } from '../utils/catalogUtils';
// Por ahora, para evitar error si no está exportada, la importaremos directamente.
import { transformRawProductToCatalogProduct } from '../utils/catalogUtils';
import type { RawProductDataForSupabase } from '../utils/catalogUtils'; // Necesitaremos exportar este tipo
import ProductCard from './ProductCard';

const CatalogSupabase = () => {
  const [products, setProducts] = useState<CatalogProduct[]>([]); // Cambiar el tipo del estado
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // Ayudar a TypeScript con el tipo de 'query'
        // Asumimos que 'supabase' no es null aquí debido a las validaciones en supabase.ts
        let query = supabase! // Usar el operador de aserción no nulo
          .from('products')
          .select('*');

        if (search) {
          query = query.ilike('name', `%${search}%`);
        }

        const { data, error } = await query;

        if (error) throw error;

        // Transformar los datos antes de establecerlos en el estado
        // Usar RawProductDataForSupabase (o un tipo similar que refleje los datos de Supabase)
        const transformedProducts = data
          ? data.map((rawProd: RawProductDataForSupabase) => transformRawProductToCatalogProduct(rawProd))
          : [];
        setProducts(transformedProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'Error fetching products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [search]);

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <input
          type="text"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          placeholder="Search products..."
          className="w-full p-2 border rounded"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onAddToCart={() => console.log('Add to cart:', product.name)} // Placeholder
            showAddToCart={true} // Placeholder
          />
        ))}
      </div>
    </div>
  );
};

export default CatalogSupabase;