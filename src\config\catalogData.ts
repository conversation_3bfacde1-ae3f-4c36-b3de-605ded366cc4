import type { CatalogCategoryNode } from '../types/catalog';

/**
 * Define la estructura principal para el catálogo de productos.
 * Los IDs deben ser únicos y pueden usarse para enrutamiento o para obtener datos específicos de la categoría.
 */
export const catalogStructure: CatalogCategoryNode[] = [
  {
    id: 'epp', // Nodo principal para todos los Equipos de Protección Personal
    name: 'Equipos de Protección Personal (EPP)',
    children: [
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'craneana', // Corresponde a PPECategory id 'craneana'
        name: 'Protec<PERSON> Craneana',
        children: [
          { id: 'craneana-dielectricos', name: '<PERSON><PERSON><PERSON>' }, // PPESubcategory 'dielectricos'
          { id: 'craneana-altura', name: '<PERSON><PERSON><PERSON> Altura' },         // PPESubcategory 'altura'
          { id: 'craneana-gorra-casquete', name: '<PERSON><PERSON> con <PERSON>' },// PPESubcategory 'gorra-casquete'
          { id: 'craneana-mentoneras', name: '<PERSON><PERSON><PERSON>' }            // PPESubcategory 'mentoneras'
        ],
      },
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'ocular', // Corresponde a PPECategory id 'ocular'
        name: 'Protección Ocular',
        children: [
          { id: 'ocular-anteojos', name: 'Anteojos de Seguridad' }, // PPESubcategory 'anteojos'
          { id: 'ocular-antiparras', name: 'Antiparras' }           // PPESubcategory 'antiparras'
        ],
      },
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'facial', // Corresponde a PPECategory id 'facial'
        name: 'Protección Facial',
        children: [
          { id: 'facial-plano', name: 'Protector Facial Plano' },
          { id: 'facial-burbuja', name: 'Protector Facial Burbuja' },
          { id: 'facial-forestal', name: 'Protector Facial Forestal' },
          { id: 'facial-deflagatoria', name: 'Protección Deflagatoria' },
          { id: 'facial-soldador', name: 'Máscara de Soldador' }
        ],
      },
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'respiratoria', // Corresponde a PPECategory id 'respiratoria'
        name: 'Protección Respiratoria',
        children: [
          { id: 'respiratoria-mascarillas-descartables', name: 'Mascarillas Descartables' },
          { id: 'respiratoria-mascaras', name: 'Máscaras Respiratorias' },
          { id: 'respiratoria-equipos-autonomos', name: 'Equipos Autónomos' }
        ],
      },
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'manos', // Corresponde a PPECategory id 'manos'
        name: 'Protección para Manos',
        children: [
          { id: 'manos-anticorte', name: 'Guantes Anticorte' },
          { id: 'manos-temperatura', name: 'Guantes para Temperatura' },
          { id: 'manos-impacto', name: 'Guantes Anti-impacto' },
          { id: 'manos-nitrilicos', name: 'Guantes Nitrílicos' },
          { id: 'manos-latex', name: 'Guantes de Látex' },
          { id: 'manos-dielectricos', name: 'Guantes Dieléctricos' }, // PPESubcategory 'dielectricos'
          { id: 'manos-descarne', name: 'Guantes de Descarne' },
          { id: 'manos-mangas', name: 'Mangas Protectoras' }
        ],
      },
      { // Anteriormente hijo de 'proteccion-personal'
        id: 'auditiva', // Corresponde a PPECategory id 'auditiva'
        name: 'Protección Auditiva',
        children: [
          { id: 'auditiva-expansibles', name: 'Expansibles Endoaurales' },
          { id: 'auditiva-copa-vincha', name: 'Protectores de Copa con Vincha' },
          { id: 'auditiva-para-cascos', name: 'Protectores para Cascos' }
        ],
      },
      // Hijos del anterior 'proteccion-corporal', ahora bajo 'epp'
      { 
        id: 'indumentaria', // Corresponde a PPECategory id 'indumentaria'
        name: 'Indumentaria',
        children: [
          { id: 'indumentaria-camisas-remeras', name: 'Camisas y Remeras' },
          { id: 'indumentaria-pantalones', name: 'Pantalones' },
          { id: 'indumentaria-camperas', name: 'Camperas' },
          { id: 'indumentaria-buzos', name: 'Buzos' },
          { id: 'indumentaria-termica', name: 'Indumentaria Térmica' },
          { id: 'indumentaria-estampados', name: 'Estampados y Bordados' },
          { id: 'indumentaria-mamelucos', name: 'Mamelucos' },
          { id: 'indumentaria-descartables', name: 'Indumentaria Descartable' } // También podría ser una categoría principal
        ],
      },
      { 
        id: 'calzado', // Corresponde a PPECategory id 'calzado'
        name: 'Calzado de Seguridad',
        children: [
          { id: 'calzado-zapatos', name: 'Zapatos de Seguridad' },
          { id: 'calzado-botinas', name: 'Botinas' },
          { id: 'calzado-zapatillas', name: 'Zapatillas de Seguridad' },
          { id: 'calzado-ultralivianos', name: 'Calzado Ultraliviano' },
          { id: 'calzado-botas-pvc', name: 'Botas PVC' }
        ],
      },
      { 
        id: 'contra-fuego', // Corresponde a PPECategory id 'contra-fuego'
        name: 'Protección Contra Fuego',
        children: [
          { id: 'contrafuego-indumentaria', name: 'Indumentaria Ignífuga' },
          { id: 'contrafuego-extincion', name: 'Equipos de Extinción' }
        ],
      },
      { // Anteriormente un nodo de primer nivel, ahora bajo 'epp'
        id: 'altura-epp', // Corresponde a PPECategory id 'altura' (trabajo en altura, no cascos)
        name: 'Protección para Trabajos en Altura',
        children: [
            { id: 'altura-arnes', name: 'Arnés de Seguridad' },
            { id: 'altura-cabo-vida', name: 'Cabo de Vida' },
            { id: 'altura-anticaida', name: 'Dispositivos Anticaída' },
            { id: 'altura-antitrauma', name: 'Equipos Antitrauma' }
        ]
      }
    ],
  },
  {
    id: 'seguridad-emergencias',
    name: 'Seguridad y Emergencias',
    // Ejemplo: { id: 'senalizacion', name: 'Señalización' }, { id: 'primeros-auxilios', name: 'Primeros Auxilios' }
  },
  {
    id: 'herramientas-equipos',
    name: 'Herramientas y Equipos',
    // Ejemplo: { id: 'herramientas-manuales', name: 'Herramientas Manuales' }
  },
  {
    id: 'limpieza-mantenimiento',
    name: 'Limpieza y Mantenimiento',
    // Ejemplo: { id: 'absorbentes-industriales', name: 'Absorbentes Industriales' }
  },
  {
    id: 'accesorios',
    name: 'Accesorios',
    // Ejemplo: { id: 'accesorios-epp', name: 'Accesorios para EPP' } (si no encajan en las subcategorías específicas)
  },
];

// Ejemplo conceptual de cómo podrías obtener productos para una categoría:
// async function getProductsForCategory(categoryId: string): Promise<CatalogProduct[]> {
//   // Esta función obtendría productos de tu backend.
//   // Los productos deberían tener un array 'categoryIds' que incluya los IDs relevantes de esta nueva estructura.
//   // Ej: un casco dieléctrico tendría en sus categoryIds: ['epp', 'craneana', 'craneana-dielectricos']
//   console.log(`Obteniendo productos para la categoría: ${categoryId}`); // Placeholder
//   return [];
// }