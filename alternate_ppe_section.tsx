import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { industriesPPE, IndustryPPE, PPEItem, groupPPEByBodyPart, bodyPartNames, BodyPart } from '../config/ppeData';
import { ChevronRight, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as supabaseClientUntyped } from '../lib/supabase';

const PPEVisualizer: React.FC = () => {
  const [selectedIndustry, setSelectedIndustry] = useState<IndustryPPE>(industriesPPE[0]);
  const [hoveredItem, setHoveredItem] = useState<PPEItem | null>(null);
  const [groupedItems, setGroupedItems] = useState<Record<string, PPEItem[]>>({});
  const navigate = useNavigate();

  const supabase: SupabaseClient = supabaseClientUntyped as SupabaseClient;

  useEffect(() => {
    setGroupedItems(groupPPEByBodyPart(selectedIndustry.ppeItems));
  }, [selectedIndustry]);

  const handleIndustrySelect = (industry: IndustryPPE) => {
    setSelectedIndustry(industry);
    setHoveredItem(null);
  };

  const navigateToCatalog = (item: PPEItem) => {
    console.log("Navegando al catálogo con:", {
      industry: selectedIndustry.name,
      subfilter: item.name
    });
    
    const industryParam = encodeURIComponent(selectedIndustry.name);
    navigate(`/catalog?industry=${industryParam}&subfilter=${encodeURIComponent(item.name)}`);
  };

  return (
    <div className="w-full min-h-[calc(100vh-80px)] flex flex-col py-12 bg-gray-900 text-white">
      <div className="container mx-auto px-4 flex flex-col flex-grow">
        <h2 className="text-3xl font-bold text-white mb-8 text-center">
          Equipos de Protección Personal por Industria
          <span className="block h-1 w-24 bg-amber-500 mt-2 rounded-full mx-auto"></span>
        </h2>

        {/* Industry Selector - Estilo mejorado similar a CTA Cards */}
        <div className="mb-8 overflow-x-auto hide-scrollbar">
          <div className="flex space-x-4 pb-6 justify-center">
            {industriesPPE.map((industry) => {
              const Icon = industry.icon;
              const isActive = selectedIndustry.id === industry.id;
              
              return (
                <button
                  key={industry.id}
                  onClick={() => handleIndustrySelect(industry)}
                  className={`
                    flex flex-col items-center justify-center p-4 rounded-xl
                    transition-all duration-300 ease-in-out
                    min-w-[120px] transform hover:-translate-y-1
                    ${isActive 
                      ? 'bg-gradient-to-br from-amber-500 to-amber-600 text-white shadow-lg shadow-amber-500/20' 
                      : 'bg-gradient-to-br from-gray-800 to-gray-700 text-gray-200 hover:from-gray-700 hover:to-gray-600'}
                  `}
                  aria-pressed={isActive}
                  aria-label={`Seleccionar industria ${industry.displayName}`}
                >
                  <div className={`
                    p-3 rounded-lg mb-2
                    ${isActive ? 'bg-amber-400 text-amber-900' : 'bg-gray-600 text-gray-200'}
                  `}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <span className="text-sm font-medium">
                    {industry.displayName}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Contenedor principal */}
        <div className="flex flex-col md:flex-row gap-8 flex-grow">
          {/* Body Visualization */}
          <div className="w-full md:w-1/2 relative rounded-xl overflow-hidden shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 to-blue-700/80 z-10"></div>
            
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedIndustry.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="relative w-full aspect-square flex items-center justify-center z-20"
              >
                {/* Imagen cargada desde Supabase Storage */}
                {selectedIndustry && (
                  <img 
                    src={supabase.storage
                          .from('static-images')
                          .getPublicUrl(`industry-selector/body-${selectedIndustry.name.toLowerCase()}.jpg`).data?.publicUrl || '/images/placeholder-product.jpg'}
                    alt={`Imagen de ${selectedIndustry.displayName}`}
                    className="max-w-full max-h-full object-contain"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-product.jpg';
                    }}
                  />
                )}
                
                {/* PPE Hotspots */}
                {selectedIndustry.ppeItems.map((item) => (
                  <motion.div
                    key={item.id}
                    className="absolute"
                    style={{
                      left: `${item.position.x}%`,
                      top: `${item.position.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: [0.8, 1, 0.8],
                      boxShadow: hoveredItem?.id === item.id 
                        ? ['0 0 0 rgba(245, 158, 11, 0.5)', '0 0 20px rgba(245, 158, 11, 0.8)', '0 0 0 rgba(245, 158, 11, 0.5)'] 
                        : ['0 0 0 rgba(245, 158, 11, 0.5)', '0 0 10px rgba(245, 158, 11, 0.5)', '0 0 0 rgba(245, 158, 11, 0.5)']
                    }}
                    transition={{ 
                      repeat: Infinity, 
                      duration: hoveredItem?.id === item.id ? 1.5 : 2.5
                    }}
                    onMouseEnter={() => setHoveredItem(item)}
                    onMouseLeave={() => setHoveredItem(null)}
                    onClick={() => navigateToCatalog(item)}
                  >
                    <div 
                      className={`
                        w-10 h-10 rounded-full flex items-center justify-center
                        ${hoveredItem?.id === item.id 
                          ? 'bg-amber-500 shadow-lg shadow-amber-500/50' 
                          : 'bg-amber-400 shadow-md shadow-amber-400/30'}
                        cursor-pointer transition-colors duration-300
                      `}
                    >
                      {React.createElement(item.icon, { 
                        className: 'w-5 h-5 text-white',
                        'aria-hidden': true
                      })}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* PPE Details Panel */}
          <div className="w-full md:w-1/2 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 overflow-y-auto shadow-2xl flex-grow border border-gray-700">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 h-full">
              {Object.entries(groupedItems).map(([bodyPart, items]) => (
                <div key={bodyPart} className="space-y-4">
                  <h4 className="text-sm font-semibold text-amber-400 uppercase px-4 py-2 bg-gray-800 rounded-lg shadow-inner border-l-4 border-amber-500">
                    {bodyPartNames[bodyPart as BodyPart] || bodyPart}
                  </h4>
                  <div className="space-y-3">
                    {items.map((item) => {
                      const Icon = item.icon;
                      const isHighlighted = hoveredItem?.id === item.id;
                      
                      return (
                        <motion.div
                          key={item.id}
                          className={`
                            p-4 rounded-lg cursor-pointer flex items-center transition-all duration-200
                            ${isHighlighted 
                              ? 'bg-gradient-to-r from-amber-500 to-amber-600 shadow-lg shadow-amber-500/20 scale-105' 
                              : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700'}
                          `}
                          animate={{ 
                            y: isHighlighted ? -2 : 0,
                          }}
                          transition={{ type: 'spring', stiffness: 300 }}
                          onMouseEnter={() => setHoveredItem(item)}
                          onMouseLeave={() => setHoveredItem(null)}
                          onClick={() => navigateToCatalog(item)}
                        >
                          <div className={`
                            w-12 h-12 flex items-center justify-center rounded-full mr-4
                            ${isHighlighted ? 'bg-amber-400' : 'bg-blue-400'}
                          `}>
                            <Icon className="w-6 h-6 text-gray-900" aria-hidden="true" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h5 className={`text-base font-medium ${isHighlighted ? 'text-white' : 'text-gray-200'} truncate`}>
                              {item.name}
                            </h5>
                            {item.standard && (
                              <p className={`text-xs ${isHighlighted ? 'text-amber-200' : 'text-blue-300'} truncate`}>
                                Norma: {item.standard}
                              </p>
                            )}
                          </div>
                          <div className={`
                            ml-2 p-2 rounded-full
                            ${isHighlighted ? 'bg-amber-400 text-amber-900' : 'bg-gray-600 text-gray-300'}
                          `}>
                            <ArrowRight className="w-4 h-4" />
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PPEVisualizer;