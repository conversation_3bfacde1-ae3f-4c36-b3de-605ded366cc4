import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// Determinar la base URL según el ambiente
const getBase = () => {
  // Ambiente local de desarrollo
  if (process.env.NODE_ENV === 'development') {
    return '/';
  }
  // Ambiente de release (preview)
  if (process.env.CF_PAGES_BRANCH === 'release') {
    return '/release-v1-0-0/';
  }
  // Ambiente de producción (main)
  return '/';
};

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [react()],
    
    server: {
      port: 5173,
      host: true,
      proxy: {
        '/api': {
          target: 'http://localhost:3010',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },

    build: {
      outDir: 'dist',
      sourcemap: true
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },

    base: getBase(),
  };
});
