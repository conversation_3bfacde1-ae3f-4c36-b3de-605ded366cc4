import React, { useState, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { PPEItem, BodyPart } from '../config/ppeData';
import { getEPPCatalogCategory } from '../config/eppCategoryMapping';

interface EPPCategoryVisualizerProps {
  ppeItems: PPEItem[];
  onCategorySelect?: (category: string, subcategory?: string) => void;
  className?: string;
}

const EPPCategoryVisualizer: React.FC<EPPCategoryVisualizerProps> = ({
  ppeItems,
  onCategorySelect,
  className = ''
}) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Obtener filtros actuales de la URL
  const currentCategory = searchParams.get('category');
  const currentSubcategory = searchParams.get('subcategory');

  const handleItemClick = useCallback((item: PPEItem) => {
    const catalogCategory = getEPPCatalogCategory(item.bodyPart);
    
    if (onCategorySelect) {
      // Usar callback si se proporciona
      onCategorySelect(catalogCategory, item.id);
    } else {
      // Navegar al catálogo con filtros aplicados
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('category', catalogCategory);
      newSearchParams.set('subcategory', item.id);
      
      navigate(`/catalog?${newSearchParams.toString()}`);
    }
  }, [onCategorySelect, navigate, searchParams]);

  const handleBodyPartClick = useCallback((bodyPart: BodyPart) => {
    const catalogCategory = getEPPCatalogCategory(bodyPart);
    
    if (onCategorySelect) {
      onCategorySelect(catalogCategory);
    } else {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('category', catalogCategory);
      newSearchParams.delete('subcategory'); // Limpiar subcategoría
      
      navigate(`/catalog?${newSearchParams.toString()}`);
    }
  }, [onCategorySelect, navigate, searchParams]);

  // Agrupar items por bodyPart
  const groupedItems = ppeItems.reduce((acc, item) => {
    if (!acc[item.bodyPart]) {
      acc[item.bodyPart] = [];
    }
    acc[item.bodyPart].push(item);
    return acc;
  }, {} as Record<BodyPart, PPEItem[]>);

  return (
    <div className={`w-full ${className}`}>
      {/* Grid de categorías principales */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
        {Object.entries(groupedItems).map(([bodyPart, items]) => {
          const catalogCategory = getEPPCatalogCategory(bodyPart as BodyPart);
          const isSelected = currentCategory === catalogCategory;
          const firstItem = items[0];
          
          return (
            <button
              key={bodyPart}
              onClick={() => handleBodyPartClick(bodyPart as BodyPart)}
              className={`
                p-4 rounded-lg border-2 transition-all duration-200 text-left
                ${isSelected 
                  ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-md' 
                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                }
              `}
            >
              <div className="flex items-center space-x-3">
                {firstItem?.icon && (
                  <firstItem.icon className={`w-6 h-6 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`} />
                )}
                <div className="flex-1">
                  <h3 className="font-medium text-sm">
                    {catalogCategory.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {items.length} {items.length === 1 ? 'producto' : 'productos'}
                  </p>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Subcategorías para la categoría seleccionada */}
      {currentCategory && groupedItems[Object.keys(groupedItems).find(bodyPart => 
        getEPPCatalogCategory(bodyPart as BodyPart) === currentCategory
      ) as BodyPart] && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">
            Productos de {currentCategory.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {Object.entries(groupedItems)
              .filter(([bodyPart]) => getEPPCatalogCategory(bodyPart as BodyPart) === currentCategory)
              .flatMap(([_, items]) => items)
              .map(item => {
                const isSelected = currentSubcategory === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    onMouseEnter={() => setHoveredItem(item.id)}
                    onMouseLeave={() => setHoveredItem(null)}
                    className={`
                      p-3 rounded-md text-left transition-all duration-200
                      ${isSelected
                        ? 'bg-blue-100 border border-blue-300 text-blue-800 shadow-sm'
                        : 'bg-white border border-gray-200 hover:bg-blue-50 hover:border-blue-200'
                      }
                    `}
                  >
                    <div className="flex items-start space-x-3">
                      {item.icon && (
                        <item.icon className={`w-5 h-5 mt-0.5 flex-shrink-0 ${
                          isSelected ? 'text-blue-600' : 'text-gray-500'
                        }`} />
                      )}
                      <div className="flex-1 min-w-0">
                        <h5 className="text-sm font-medium truncate">{item.name}</h5>
                        {item.description && (
                          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                            {item.description}
                          </p>
                        )}
                        {item.standard && (
                          <p className="text-xs text-blue-600 mt-1 font-medium">
                            {item.standard}
                          </p>
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
          </div>
        </div>
      )}
    </div>
  );
};

export default EPPCategoryVisualizer;