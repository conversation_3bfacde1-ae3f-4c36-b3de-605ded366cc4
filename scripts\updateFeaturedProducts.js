import { supabase } from '../src/lib/supabase';

const updateFeaturedProducts = async () => {
  try {
    // 1. Eliminar productos destacados expirados
    const { error: deleteError } = await supabase
      .from('featured_products')
      .delete()
      .lt('end_date', new Date().toISOString());

    if (deleteError) throw deleteError;

    // 2. Obtener productos populares o con mejor rating para destacarlos
    const { data: topProducts, error: productsError } = await supabase
      .from('products')
      .select('id')
      .order('stock', { ascending: false })
      .limit(5);

    if (productsError) throw productsError;

    // 3. Preparar nuevos productos destacados
    const newFeaturedProducts = topProducts.map((product, index) => ({
      product_id: product.id,
      featured_type: index === 0 ? 'top' : 'featured',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 días
    }));

    // 4. Insertar nuevos productos destacados
    const { error: insertError } = await supabase
      .from('featured_products')
      .upsert(newFeaturedProducts, {
        onConflict: 'product_id',
        ignoreDuplicates: false
      });

    if (insertError) throw insertError;

    console.log('Featured products updated successfully');
  } catch (error) {
    console.error('Error updating featured products:', error);
    throw error;
  }
};

// Función para marcar productos como "nuevos"
const markNewProducts = async () => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Obtener productos creados en los últimos 30 días
    const { data: newProducts, error: queryError } = await supabase
      .from('products')
      .select('id')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .limit(10);

    if (queryError) throw queryError;

    // Marcar productos como nuevos
    const newFeaturedEntries = newProducts.map(product => ({
      product_id: product.id,
      featured_type: 'new',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 días
    }));

    const { error: insertError } = await supabase
      .from('featured_products')
      .upsert(newFeaturedEntries, {
        onConflict: 'product_id',
        ignoreDuplicates: false
      });

    if (insertError) throw insertError;

    console.log('New products marked successfully');
  } catch (error) {
    console.error('Error marking new products:', error);
    throw error;
  }
};

// Ejecutar actualizaciones
const main = async () => {
  try {
    await updateFeaturedProducts();
    await markNewProducts();
  } catch (error) {
    console.error('Error in main execution:', error);
    process.exit(1);
  }
};

main();