@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Importar estilos de categorías */
@import './components/Categories.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Eliminar la importación de CategoriesGrid.css */
/* @import './components/CategoriesGrid.css'; */

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  h1 {
    @apply text-4xl font-bold text-[#0F172A];
  }
  
  h2 {
    @apply text-3xl font-semibold text-[#0F172A];
  }
  
  h3 {
    @apply text-2xl font-semibold text-[#0F172A];
  }
  
  p {
    color: #1E293B;
    font-size: 1rem;
    line-height: 1.625;
  }

  /* Override para el PromoSlider */
  .promo-slider h2,
  .promo-slider h3,
  .promo-slider p {
    @apply text-white;
  }
}

@layer components {
  .text-heading {
    @apply text-[#0F172A];
  }
  
  .text-body {
    @apply text-[#1E293B];
  }
}

/* Ocultar scrollbar pero mantener funcionalidad */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Establecer una jerarquía clara de z-index */
:root {
  --z-base: 0;
  --z-content: 1;
  --z-overlay: 50;
  --z-modal: 100;
  --z-navbar: 1000;
  
  /* Add navbar height variables */
  --navbar-height: 60px; /* Default for mobile */
}

@media (min-width: 768px) {
  :root {
    --navbar-height: 72px; /* For desktop */
  }
}

/* Asegurar que el contenido principal tenga un z-index apropiado */
main {
  position: relative;
  z-index: var(--z-base);
}

/* Asegurar que las cards tengan un z-index apropiado */
.feature-card {
  position: relative;
  z-index: var(--z-content);
}

/* El navbar siempre debe estar por encima */
.navbar {
  position: sticky;
  top: 0;
  z-index: var(--z-navbar);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: #FDEBD0;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #F39C12;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #D68910;
}

/* Mejoras visuales para el Hero */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.hero-gradient-text {
  background: linear-gradient(90deg, #F59E0B, #FCD34D, #F59E0B);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradientFlow 3s ease infinite;
}

/* Mejoras para los CTAs */
@media (max-width: 640px) {
  .hero-cta {
    width: 100%;
    justify-content: center;
    padding: 1.25rem 2rem;
    font-size: 1.125rem;
  }
}

/* Efecto de brillo para CTAs */
.hero-cta-primary::before,
.hero-cta-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.hero-cta-primary:hover::before,
.hero-cta-secondary:hover::before {
  left: 100%;
}

/* Efectos de gradiente para textos */
.hero-gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-amber-300 to-amber-400;
  background-size: 200% auto;
  animation: gradient 8s linear infinite;
}

@keyframes gradient {
  0% { background-position: 0% center; }
  50% { background-position: 100% center; }
  100% { background-position: 0% center; }
}

/* Mejoras para dispositivos móviles */
@media (max-width: 640px) {
  .hero-gradient-text {
    display: inline-block;
  }
}

/* Animaciones para la pantalla de carga */
@keyframes pulse-shadow {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

.loading-pulse {
  animation: pulse-shadow 1.5s infinite;
}

/* Asegurar que la pantalla de carga esté por encima de todo */
.loading-screen {
  z-index: 9999;
}

/* Estilos para la sección de feature cards horizontal */
.feature-cards-horizontal {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  .feature-cards-horizontal {
    gap: 0.75rem;
  }
}

/* Animación de pulso para los puntos de EPP */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Transiciones suaves para cambios de estado */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Estilo para scrollbar personalizado en el panel de EPP */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(203, 213, 225, 0.5);
  border-radius: 20px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.7);
}
