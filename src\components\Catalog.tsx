import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductFilters, PaginatedResponse } from '../types'; // Product se reemplazará
import type { CatalogProduct } from '../types/catalog'; // Importar CatalogProduct
import { transformRawProductToCatalogProduct, RawProductDataForSupabase } from '../utils/catalogUtils'; // Importar transformador y tipo Raw
import { z } from 'zod';
import ProductCard from './ProductCard';
import { industries } from '../utils'; // 'industries' podría necesitar revisión si se relaciona con la nueva estructura
import { catalogStructure } from '../config/catalogData'; // Importar la nueva estructura de catálogo
import IndustryFilter from './IndustryFilter';
import { Search, Badge, ChevronDown } from 'lucide-react';
import Pagination from './Pagination';
import { X } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { Link } from 'react-router-dom';
import { useDebounce } from '../hooks/useDebounce';
// INDUSTRY SELECTOR LOGIC - COMMENTED OUT (UI removed, logic preserved for future reference)
// import IndustrySelector from './IndustrySelector';
import { toast } from 'react-toastify';
import { FEATURE_FLAGS } from '../config/featureFlags';
// import { selectorSubfilterToCatalogIdMap } from '../config/selectorMappings';
// import { industrySubfilters as originalIndustrySubfiltersData } from '../config/industrySubfilters'; // Para obtener los nombres/IDs de los subfiltros del selector
import IndustryBanner from './IndustryBanner';
import type { CatalogCategoryNode } from '../types/catalog';

// Schema validation
export const ProductSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  price: z.number(),
  category: z.string(),
  industry: z.string().nullable(),
  brand: z.string().nullable(),
  stock: z.number(),
});

interface CatalogProps {
  onAddToCart: (product: CatalogProduct, quantity: number) => void; // Usar CatalogProduct
}

const Catalog: React.FC<CatalogProps> = ({ onAddToCart }) => {
  const { isAuthenticated } = useAuth();

  // Función para manejar la adición al carrito
  const handleAddToCart = useCallback((product: CatalogProduct, quantity: number) => { // Usar CatalogProduct
    if (FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated) {
      onAddToCart(product, quantity);
    } else {
      // Redirigir al login o mostrar mensaje
      toast.error('Debes iniciar sesión para agregar productos al carrito');
    }
  }, [isAuthenticated, onAddToCart]);

  // Estados
  const [searchParams, setSearchParams] = useSearchParams();
  const [search, setSearch] = useState(searchParams.get('search') || '');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [showFilters, setShowFilters] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  // Estados de filtro que se sincronizarán con la URL
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>(() => {
    const categoryParam = searchParams.get('category');
    const subcategoryParam = searchParams.get('subcategory');
    const categoriesParam = searchParams.get('categories');
    const ids: string[] = [];
    if (categoryParam) {
      ids.push(decodeURIComponent(categoryParam));
      if (subcategoryParam) {
        ids.push(decodeURIComponent(subcategoryParam));
      }
    } else if (categoriesParam) {
      decodeURIComponent(categoriesParam).split(',').forEach(id => {
        if (id.trim()) ids.push(id.trim());
      });
    }
    return ids;
  });
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>(() => {
    const industryParam = searchParams.get('industry');
    return industryParam ? decodeURIComponent(industryParam).toUpperCase().split(',') : [];
  });
  const [selectedBrands, setSelectedBrands] = useState<string[]>(() => {
    const brandParam = searchParams.get('brand');
    return brandParam ? decodeURIComponent(brandParam).toUpperCase().split(',') : [];
  });
  const [localSearch, setLocalSearch] = useState(searchParams.get('search') || '');
  // INDUSTRY SELECTOR STATES - COMMENTED OUT (UI removed, logic preserved for future reference)
  // const [selectedIndustryView, setSelectedIndustryView] = useState<string | null>(searchParams.get('industry') ? decodeURIComponent(searchParams.get('industry')!).toUpperCase().split(',')[0] : null);
  // const [isIndustrySelectorCollapsed, setIsIndustrySelectorCollapsed] = useState(false);
  const debouncedSearch = useDebounce(localSearch, 300);

  // INDUSTRY SELECTOR SUBFILTERS - COMMENTED OUT (UI removed, logic preserved for future reference)
  // Definir los subfiltros por industria (similar a IndustrySelector)
  // const industrySubfilters = {
  //   'MINERIA': ['Cascos', 'Guantes', 'Calzado'],
  //   'CONSTRUCCION': ['Cascos', 'Arnés', 'Chalecos', 'Guantes', 'Calzado', 'Anteojos'],
  //   'MANUFACTURA': ['Guantes', 'Mascarillas', 'Protección auditiva'],
  //   'PETROLERA': ['Trajes ignífugos', 'Respiradores', 'Cascos'],
  //   'QUIMICA': ['Trajes químicos', 'Respiradores', 'Guantes químicos'],
  //   'METALMECANICA': ['Protección facial', 'Guantes', 'Delantales'],
  //   'LOGISTICA': ['Fajas', 'Guantes', 'Calzado de seguridad'],
  // };

  // INDUSTRY SELECTOR HANDLERS - COMMENTED OUT (UI removed, logic preserved for future reference)
  // const handleIndustrySelect = (industry: string) => {
  //   setSelectedIndustryView(industry);
  //   handleIndustryChange(industry); // Esto actualiza selectedIndustries
  //   // No limpiar selectedSubfilters aquí, ya que no existe más de esa forma.
  //   // La selección de subfiltros del selector ahora afectará directamente a selectedCategoryIds.
  //   setIsIndustrySelectorCollapsed(false);
  // };

  // const handleSelectorSubfilterSelect = (subfilterNameFromSelector: string) => {
  //   if (selectedIndustryView) {
  //     const industryKey = selectedIndustryView.toUpperCase();
  //     const catalogId = selectorSubfilterToCatalogIdMap[industryKey]?.[subfilterNameFromSelector];
  //
  //     if (catalogId) {
  //       // Si el ID de categoría no está ya en selectedCategoryIds, lo añadimos.
  //       // Esto permite que el usuario seleccione múltiples categorías a través de este mecanismo si lo desea.
  //       // Si se quisiera que solo una categoría (la del subfiltro) esté activa,
  //       // se podría hacer setSelectedCategoryIds([catalogId])
  //       if (!selectedCategoryIds.includes(catalogId)) {
  //         // Llamar a handleCategoryChange para añadirlo a selectedCategoryIds y resetear página
  //          handleCategoryChange(catalogId);
  //       }
  //     } else {
  //       console.warn(`No catalog ID mapping for Industry: ${industryKey}, Selector Subfilter: ${subfilterNameFromSelector}`);
  //     }
  //   }
  // };

  // INDUSTRY SELECTOR BACK HANDLER - COMMENTED OUT (UI removed, logic preserved for future reference)
  // Al volver al selector de industrias, limpiar industrias y subfiltros seleccionados
  // const handleBackToIndustries = () => {
  //   if (selectedIndustryView) {
  //     const industryToRemove = selectedIndustryView;
  //     setSelectedIndustries(prev => prev.filter(ind => ind !== industryToRemove));
  //   }
  //   setSelectedIndustryView(null);
  //   // No es necesario limpiar selectedCategoryIds aquí, el usuario decide si los mantiene.
  // };

  // INDUSTRY SELECTOR ACTIVE SUBFILTERS - COMMENTED OUT (UI removed, logic preserved for future reference)
  // Ya no necesitamos subfiltersForSelectorUI, ya que IndustrySelector obtiene su lista internamente.

  // Determinar qué subfiltros del selector (por NOMBRE) están "activos"
  // porque su categoría mapeada está en selectedCategoryIds.
  // Esto se pasa a IndustrySelector para que pueda marcar visualmente los subfiltros seleccionados.
  // const activeSelectorSubfilterNames = useMemo(() => {
  //   if (!selectedIndustryView || selectedCategoryIds.length === 0) {
  //     return [];
  //   }
  //
  //   const industryKey = selectedIndustryView.toUpperCase();
  //   const industryMapping = selectorSubfilterToCatalogIdMap[industryKey];
  //   if (!industryMapping) {
  //     return [];
  //   }

  //   const activeNames: string[] = [];
  //   const originalSubfiltersForCurrentIndustry = originalIndustrySubfiltersData[industryKey] || [];

  //   originalSubfiltersForCurrentIndustry.forEach((subfilterItem: { name: string }) => {
  //     const mappedCatalogId = industryMapping[subfilterItem.name];
  //     if (mappedCatalogId && selectedCategoryIds.includes(mappedCatalogId)) {
  //       activeNames.push(subfilterItem.name);
  //     }
  //   });
  //   return activeNames;
  // }, [selectedIndustryView, selectedCategoryIds, selectorSubfilterToCatalogIdMap, originalIndustrySubfiltersData]);

  // Obtener la industria seleccionada para el banner
  const selectedIndustryForBanner = useMemo(() => {
    console.log('Processing selectedIndustries:', {
      selectedIndustries,
      length: selectedIndustries.length
    });
    
    if (selectedIndustries.length === 1) {
      const industry = selectedIndustries[0].toUpperCase();
      console.log('Selected industry for banner:', industry);
      return industry;
    }
    return null;
  }, [selectedIndustries]);

  // Añade este useEffect para monitorear cambios
  useEffect(() => {
    console.log('Selected industry for banner changed:', selectedIndustryForBanner);
  }, [selectedIndustryForBanner]);

  // INDUSTRY SELECTOR COLLAPSE HANDLER - COMMENTED OUT (UI removed, logic preserved for future reference)
  // Add a function to handle collapse toggle from IndustrySelector
  // const handleIndustrySelectorCollapse = (collapsed: boolean) => {
  //   setIsIndustrySelectorCollapsed(collapsed);
  // };

  // 2. Query client
  const queryClient = useQueryClient();

  // Efecto para sincronizar los filtros DESDE los parámetros de la URL
  useEffect(() => {
    const categoryParam = searchParams.get('category');
    const subcategoryParam = searchParams.get('subcategory');
    const categoriesUrlParam = searchParams.get('categories');
    const industryParam = searchParams.get('industry');
    const brandParam = searchParams.get('brand');
    const searchUrlParam = searchParams.get('search');

    let hasChanged = false;

    // Sincronizar categorías
    const newCategoryIdsFromUrl: string[] = [];
    if (categoryParam) {
      newCategoryIdsFromUrl.push(decodeURIComponent(categoryParam));
      if (subcategoryParam) {
        newCategoryIdsFromUrl.push(decodeURIComponent(subcategoryParam));
      }
    } else if (categoriesUrlParam) {
      decodeURIComponent(categoriesUrlParam).split(',').forEach(id => {
        if (id.trim()) newCategoryIdsFromUrl.push(id.trim());
      });
    }

    setSelectedCategoryIds(currentSelectedIds => {
      const sortedNewUrlIds = [...newCategoryIdsFromUrl].sort();
      const sortedCurrentSelectedIds = [...currentSelectedIds].sort();
      if (JSON.stringify(sortedNewUrlIds) !== JSON.stringify(sortedCurrentSelectedIds)) {
        hasChanged = true;
        return newCategoryIdsFromUrl;
      }
      return currentSelectedIds;
    });

    // Sincronizar industrias
    const newIndustriesFromUrl = industryParam ? decodeURIComponent(industryParam).toUpperCase().split(',') : [];
    setSelectedIndustries(currentSelectedIndustries => {
      if (JSON.stringify(newIndustriesFromUrl.sort()) !== JSON.stringify([...currentSelectedIndustries].sort())) {
        hasChanged = true;
        return newIndustriesFromUrl;
      }
      return currentSelectedIndustries;
    });

    // INDUSTRY SELECTOR URL SYNC - COMMENTED OUT (UI removed, logic preserved for future reference)
    // Sincronizar selectedIndustryView si es relevante
    // if (FEATURE_FLAGS.USE_INDUSTRY_SELECTOR) {
    //     const singleIndustryFromUrl = newIndustriesFromUrl.length === 1 ? newIndustriesFromUrl[0] : null;
    //     if (selectedIndustryView !== singleIndustryFromUrl) {
    //         setSelectedIndustryView(singleIndustryFromUrl);
    //     }
    // } else if (selectedIndustryView !== null) {
    //     setSelectedIndustryView(null); // Limpiar si no se usa el selector
    // }

    // Sincronizar marcas
    const newBrandsFromUrl = brandParam ? decodeURIComponent(brandParam).toUpperCase().split(',') : [];
    setSelectedBrands(currentSelectedBrands => {
      if (JSON.stringify(newBrandsFromUrl.sort()) !== JSON.stringify([...currentSelectedBrands].sort())) {
        hasChanged = true;
        return newBrandsFromUrl;
      }
      return currentSelectedBrands;
    });

    // Sincronizar búsqueda local (que alimenta debouncedSearch)
    const newSearchFromUrl = searchUrlParam || '';
    if (localSearch !== newSearchFromUrl) {
        setLocalSearch(newSearchFromUrl);
        // 'search' (el estado global) se actualiza en el useEffect de debouncedSearch
        hasChanged = true;
    }

    if (hasChanged) {
      setCurrentPage(1); // Resetear paginación si algún filtro de URL cambió el estado
    }
  }, [searchParams]); // Quitar localSearch de las dependencias

  // 3. Fetch products function
  // Esta función parece no estar siendo utilizada por el useQuery principal.
  // Si se usa en otro lugar, también necesitará ser adaptada para CatalogProduct.
  // Por ahora, nos enfocamos en el useQuery que sí se usa para poblar productsResponse.
  const fetchProducts = useCallback(async (filters: ProductFilters): Promise<PaginatedResponse<CatalogProduct>> => {
    const { search, industries } = filters; // 'categories' también está en ProductFilters
    try {
      let query = supabase! // Asumimos que supabase está inicializado
        .from('products')
        .select('*', { count: 'exact' });

      if (search) {
        query = query.ilike('name', `%${search}%`);
      }

      if (industries && industries.length > 0) {
        // Asumiendo que 'industry' en la DB es un string simple, no un array.
        // Si 'industry' es un array en la DB, usar 'cs' o 'ov' (contains, overlaps)
        query = query.in('industry', industries);
      }
      
      // Aquí se podría añadir filtrado por categorías si 'filters.categories' se usa
      // y si el campo 'categories' en la DB (que es text[]) contiene los IDs de catalogStructure.
      // if (filters.categories && filters.categories.length > 0) {
      //   query = query.cs('categories', filters.categories); // 'categories' CONTAINS selectedCategoryIds
      // }


      const { data, error, count } = await query;

      if (error) throw error;
      if (!data) return { data: [], count: 0 };

      const transformedData = data.map((p: RawProductDataForSupabase) => transformRawProductToCatalogProduct(p));
      
      return {
        data: transformedData,
        count: count || transformedData.length
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      return { data: [], count: 0 };
    }
  }, []);

  // 4. Query hook
  const {
    data: productsResponse,
    isLoading,
    error,
    isFetching
  } = useQuery<{ data: CatalogProduct[]; count: number }, Error>({ // Usar CatalogProduct
    queryKey: ['products'], // Considerar añadir filtros a la queryKey si la queryFn los usa
    queryFn: async () => {
      const { data, error } = await supabase! // Asumimos que supabase está inicializado
        .from('products')
        .select('*');

      if (error) throw error;
      if (!data) return { data: [], count: 0 };
      
      const transformedData = data.map((p: RawProductDataForSupabase) => transformRawProductToCatalogProduct(p));
      return { data: transformedData, count: transformedData.length };
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Crear un mapa de ID de categoría a nombre de categoría para búsqueda eficiente
  const categoryIdToNameMap = useMemo(() => {
    const map = new Map<string, string>();
    const populateMap = (nodes: CatalogCategoryNode[]) => {
      for (const node of nodes) {
        map.set(node.id, node.name);
        if (node.children) {
          populateMap(node.children);
        }
      }
    };
    populateMap(catalogStructure); // catalogStructure se importa y es constante
    return map;
  }, []);

  // 5. Memoized values
  const filteredProducts = useMemo(() => {
    if (!productsResponse?.data) return { data: [], count: 0 };

    const urlCategory = searchParams.get('category');
    const urlSubcategory = searchParams.get('subcategory');

    let filtered = [...productsResponse.data];

    // Filtrar por búsqueda local usando debouncedSearch
    if (debouncedSearch) {
      const searchLower = debouncedSearch.toLowerCase();
      filtered = filtered.filter(product =>
        // Buscar en nombre
        product.name.toLowerCase().includes(searchLower) ||
        // Buscar en descripción (verificando que exista)
        (product.description?.toLowerCase().includes(searchLower)) ||
        // Buscar en marca (verificando que exista)
        (product.brand?.toLowerCase().includes(searchLower)) ||
        // Buscar en categorías (usar categoryIds)
        (Array.isArray(product.categoryIds) && product.categoryIds.some(catId => {
            const categoryName = categoryIdToNameMap.get(catId);
            return categoryName?.toLowerCase().includes(searchLower);
          })
        )
      );
    }

    // Aplicar filtros adicionales

    // Ya no necesitamos getCategoryIdsByNames aquí porque selectedCategoryIds almacenará IDs directamente.
    
    if (selectedCategoryIds.length > 0) {
      filtered = filtered.filter(product => {
        const productCats = product.categoryIds || [];

        // Determinar si estamos en el caso de navegación desde EPPCategoryVisualizer
        // con category Y subcategory presentes en la URL, y si esos filtros aún están activos.
        const пришлиСEpPVisualizerСПодкатегорией = 
          urlCategory && 
          urlSubcategory &&
          selectedCategoryIds.includes(urlCategory) && 
          selectedCategoryIds.includes(urlSubcategory);

        if (пришлиСEpPVisualizerСПодкатегорией) {
          // Filtro AND: el producto debe tener AMBOS, el category y el subcategory de la URL.
          return productCats.includes(urlCategory) && productCats.includes(urlSubcategory);
        } else {
          // Filtro OR: el producto debe tener AL MENOS UNO de los IDs en selectedCategoryIds.
          // Cubre navegación solo con 'category', o selección directa desde IndustryFilter.
          return selectedCategoryIds.some(idToFilter => productCats.includes(idToFilter));
        }
      });
    }
    
    if (selectedIndustries.length > 0) {
      filtered = filtered.filter(product =>
        product.industries && product.industries.some(ind => selectedIndustries.includes(ind)) // Adaptar para array de industrias
      );
    }

    if (selectedBrands.length > 0) {
      filtered = filtered.filter(product => 
        product.brand && selectedBrands.includes(product.brand.toUpperCase()) // Convertir marca del producto a mayúsculas para comparar
      );
    }

    // El filtrado por selectedSubfilters ya no es necesario aquí,
    // porque la selección del subfiltro del selector ahora actualiza selectedCategoryIds,
    // y el filtro principal de categorías (usando selectedCategoryIds) ya se encarga de ello.

    // Calcular paginación
    const totalCount = filtered.length;
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    
    return {
      data: filtered.slice(start, end),
      count: totalCount
    };
  }, [
    productsResponse?.data,
    debouncedSearch,
    selectedCategoryIds,
    selectedIndustries,
    selectedBrands,
    // selectedSubfilters, // Ya no es una dependencia directa del filtro de productos
    currentPage, // Asegúrate que categoryIdToNameMap esté disponible si se mueve este useMemo
    categoryIdToNameMap,
    itemsPerPage,
    searchParams // Añadido searchParams como dependencia
  ]);

  // 6. Debug effect
  useEffect(() => {
    console.log('Current state:', {
      productsResponse,
      isLoading,
      error,
      isFetching,
      filteredProducts
    });
  }, [productsResponse, isLoading, error, isFetching, filteredProducts]);

  // uniqueCategories ahora se basará en catalogStructure
  // Necesitaremos una forma de presentar esto al IndustryFilter,
  // que podría esperar un array plano de strings o una estructura más compleja.
  // Por ahora, vamos a aplanar la estructura para obtener todos los nombres de categoría.
  // Esto es una simplificación; idealmente, IndustryFilter manejaría la jerarquía.
  
  const getCategoryNamesFromStructure = (nodes: typeof catalogStructure): string[] => {
    let names: string[] = [];
    nodes.forEach(node => {
      names.push(node.name); // Usar el nombre para mostrar
      if (node.children) {
        names = names.concat(getCategoryNamesFromStructure(node.children));
      }
    });
    return names;
  };

  const uniqueCategoriesForDisplay = useMemo(() => {
    // Esta es una lista plana de nombres para el filtro actual.
    // El filtro de categorías deberá mapear estos nombres a los IDs correctos para la lógica de filtrado.
    return getCategoryNamesFromStructure(catalogStructure);
  }, []);


  const uniqueBrands = useMemo(() => {
    if (!productsResponse?.data) return [];
    return Array.from(new Set(
      productsResponse.data
        .map(product => product.brand?.toUpperCase()) // Convertir a mayúsculas
        .filter(Boolean) as string[] // Asegurar que el resultado es string[]
    ));
  }, [productsResponse?.data]);

  // Subfiltros: nombres únicos de productos filtrados por categoría e industria
  const subfilters = useMemo(() => {
    if (!productsResponse?.data) return [];
    let filtered = [...productsResponse.data];
    if (selectedCategoryIds.length > 0) { // Corregido
      filtered = filtered.filter(product =>
        selectedCategoryIds.some((cat: string) => (product.categoryIds || []).includes(cat)) // Corregido y tipado cat
      );
    }
    if (selectedIndustries.length > 0) {
      filtered = filtered.filter(product =>
        // Adaptar para array de industrias, convirtiendo la industria del producto a mayúsculas
        product.industries && product.industries.some(ind => selectedIndustries.includes(ind.toUpperCase()))
      );
    }
    // Retornar nombres únicos de producto
    return Array.from(new Set(filtered.map(product => product.name)));
  }, [productsResponse?.data, selectedCategoryIds, selectedIndustries]);

  // 6. Event handlers
  // handleSubfilterChange y el estado selectedSubfilters se eliminaron.
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearch(value);
    // No actualizar searchParams aquí
  }, []);

  const handleIndustryChange = useCallback((industry: string) => {
    const upperCaseBrand = brand.toUpperCase(); // Convertir a mayúsculas
    setSelectedIndustries((prev: string[]) => 
      {
        const newSelected = prev.includes(upperCaseBrand) ? prev.filter(b => b !== upperCaseBrand) : [...prev, upperCaseBrand];
        setSearchParams(currentParams => {
          const newParams = new URLSearchParams(currentParams);
          if (newSelected.length > 0) {
            newParams.set('industry', newSelected.join(','));
          } else {
            newParams.delete('industry');
          }
          if (newParams.toString() !== currentParams.toString()) {
            setCurrentPage(1);
            return newParams;
          }
          return currentParams;
        }, { replace: true });
        return newSelected;
      }
    );
    // INDUSTRY SELECTOR FILTER SYNC - COMMENTED OUT (UI removed, logic preserved for future reference)
    // Si se usa IndustrySelector, y se cambia la industria desde el filtro IndustryFilter,
    // actualizamos selectedIndustryView si solo queda una industria seleccionada.
    // if (FEATURE_FLAGS.USE_INDUSTRY_SELECTOR) {
    //     setSelectedIndustries(currentSelected => {
    //         if (currentSelected.length === 1) {
    //             setSelectedIndustryView(currentSelected[0]);
    //         } else {
    //             setSelectedIndustryView(null);
    //         }
    //         return currentSelected; // Devuelve el estado actual para que el flujo continúe
    //     });
    // }
  }, [setSearchParams]);

  const handleBrandChange = useCallback((brand: string) => {
    const upperCaseBrand = brand.toUpperCase(); // Convertir a mayúsculas
    setSelectedBrands((prev: string[]) => 
      {
        const newSelected = prev.includes(upperCaseBrand) ? prev.filter(b => b !== upperCaseBrand) : [...prev, upperCaseBrand];
        setSearchParams(currentParams => {
          const newParams = new URLSearchParams(currentParams);
          if (newSelected.length > 0) {
            newParams.set('brand', newSelected.join(','));
          } else {
            newParams.delete('brand');
          }
          if (newParams.toString() !== currentParams.toString()) {
            setCurrentPage(1);
            return newParams;
          }
          return currentParams;
        }, { replace: true });
        return newSelected;
      }
    );
  }, [setSearchParams, setSelectedBrands, setCurrentPage]); // Añadidas dependencias

  const handleCategoryChange = useCallback((categoryId: string) => { // Ahora recibe y maneja IDs
    setSelectedCategoryIds((prev: string[]) =>
      {
        const newSelected = prev.includes(categoryId) ? prev.filter(id => id !== categoryId) : [...prev, categoryId];
        setSearchParams(currentParams => {
          const newParams = new URLSearchParams(currentParams);
          newParams.delete('category'); // Limpiar params específicos si se usa el filtro general
          newParams.delete('subcategory');
          if (newSelected.length > 0) {
            newParams.set('categories', newSelected.join(','));
          } else {
            newParams.delete('categories');
          }
          if (newParams.toString() !== currentParams.toString()) {
            setCurrentPage(1);
            return newParams;
          }
          return currentParams;
        }, { replace: true });
        return newSelected;
      }
    );
  }, [setSearchParams, setSelectedCategoryIds, setCurrentPage]); // Añadidas dependencias

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset a la primera página cuando cambia el número de items por página
  }, []);

  const handleLocalSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearch(e.target.value);
    setCurrentPage(1); // Resetear a la primera página cuando se busca
  }, []);

  // handleSubfilterSelect se renombró a handleSelectorSubfilterSelect y su lógica cambió.
  // La prop onSubfilterSelect del IndustrySelector se conectará a handleSelectorSubfilterSelect.
  
  // Efecto para actualizar el parámetro 'search' en la URL cuando debouncedSearch cambia
  useEffect(() => {
    if (debouncedSearch === undefined) return; // No hacer nada si es el valor inicial de useDebounce

    if (debouncedSearch !== undefined) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set('search', debouncedSearch);
        } else {
          newParams.delete('search');
        }
        if (newParams.toString() !== prev.toString()) {
          setCurrentPage(1); 
          return newParams;
        }
        return prev;
      }, { replace: true }); // Usar replace para no llenar el historial del navegador
      setSearch(debouncedSearch);
    }
  }, [debouncedSearch, setSearchParams, setSearch]);

  if (isLoading || isFetching) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-lg text-gray-600">Cargando productos...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-lg text-red-500">
          Error al cargar productos: {(error as Error).message}
        </div>
      </div>
    );
  }

  // Verificar si tenemos datos antes de renderizar
  const hasProducts = productsResponse?.data && productsResponse.data.length > 0;
  const displayProducts = filteredProducts?.data || [];

  return (
    <div className="container mx-auto px-4 pt-16">
      {/* Industry Banner - Selector de industrias removido, solo banner activo */}
      <div className="my-6">
        {/* INDUSTRY SELECTOR RENDERING - COMMENTED OUT (UI removed, logic preserved for future reference) */}
        {/* {FEATURE_FLAGS.USE_INDUSTRY_SELECTOR ? (
          <IndustrySelector
            selectedIndustry={selectedIndustryView}
            onIndustrySelect={handleIndustrySelect}
            onBack={handleBackToIndustries}
            onSubfilterSelect={handleSelectorSubfilterSelect}
            selectedSubfilters={activeSelectorSubfilterNames} // Prop correcta para los nombres de subfiltros seleccionados
            isCollapsed={isIndustrySelectorCollapsed}
            onCollapseToggle={handleIndustrySelectorCollapse}
          />
        ) : ( */}
          <IndustryBanner
            selectedIndustry={selectedIndustryForBanner}
            selectedIndustriesCount={selectedIndustries.length}
          />
        {/* )} */}
      </div>

      {/* Layout principal */}
      <div className="flex flex-col lg:flex-row gap-8 mt-8">
        {/* Sidebar desktop */}
        <aside className="hidden lg:block w-72 flex-shrink-0">
          <div className="sticky top-20">
            <IndustryFilter
              allIndustries={industries}
              selectedIndustries={selectedIndustries}
              onIndustryChange={handleIndustryChange}
              brands={uniqueBrands}
              selectedBrands={selectedBrands}
              onBrandChange={handleBrandChange}
              // categories={uniqueCategoriesForDisplay} // Ya no se pasa la lista plana de nombres
              categoryStructure={catalogStructure}
              selectedCategories={selectedCategoryIds}
              onCategoryChange={handleCategoryChange}
              // Las props relevantCategory... se eliminan de IndustryFilter
            />
          </div>
        </aside>

        {/* Contenedor de productos con su propio buscador sticky */}
        <div className="flex-1">
          {/* Buscador destacado */}
          <div className="sticky top-20 z-20 bg-white/80 backdrop-blur-sm py-2 border-b border-gray-100 shadow-sm mb-4">
            <div className="relative">
              <input
                type="text"
                value={localSearch}
                onChange={handleLocalSearchChange}
                placeholder="Buscar productos por nombre, descripción, marca o categoría..."
                className="w-full pl-10 pr-4 py-2 bg-white border border-amber-200 rounded-xl 
                         shadow-sm focus:ring-2 focus:ring-amber-500 focus:border-transparent
                         transition-all duration-200"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500 w-5 h-5" />
              {localSearch && (
                <button
                  onClick={() => setLocalSearch('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Grid de productos - Tamaño consistente como la sección indumentaria */}
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4 auto-rows-fr">
            {displayProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onAddToCart={handleAddToCart}
                showAddToCart={FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated}
              />
            ))}
          </div>

          {!isAuthenticated && !FEATURE_FLAGS.ALLOW_GUEST_CART && (
            <div className="mt-8 p-4 bg-gray-50 rounded-lg text-center">
              <p className="text-gray-700">
                Para agregar productos al carrito, por favor{' '}
                <Link to="/login" className="text-amber-600 hover:text-amber-700 font-medium">
                  inicia sesión
                </Link>
              </p>
            </div>
          )}

          {/* Paginación */}
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil((filteredProducts?.count || 0) / itemsPerPage)}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Catalog;
