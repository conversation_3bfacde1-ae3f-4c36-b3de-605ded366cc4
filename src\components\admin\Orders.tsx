import React, { useState } from 'react';
import DemoBanner from '../DemoBanner';
import { Search, Filter, Package, Clock, CheckCircle, XCircle } from 'lucide-react';

const Orders = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const mockOrders = [
    {
      id: '#ORD-001',
      customer: '<PERSON>',
      date: '2024-01-15',
      total: '$299.99',
      status: 'Completado',
      items: 3
    },
    {
      id: '#ORD-002',
      customer: '<PERSON>',
      date: '2024-01-15',
      total: '$159.99',
      status: 'Pendiente',
      items: 2
    },
    {
      id: '#ORD-003',
      customer: '<PERSON>',
      date: '2024-01-14',
      total: '$499.99',
      status: 'En Proceso',
      items: 5
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completado':
        return 'bg-green-100 text-green-800';
      case 'Pendiente':
        return 'bg-yellow-100 text-yellow-800';
      case 'En Proceso':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completado':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'Pendiente':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'En Proceso':
        return <Package className="w-4 h-4 text-blue-600" />;
      default:
        return <XCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="pt-16">
      <DemoBanner />
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Gestión de Pedidos</h1>

        {/* Filters and Search */}
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar pedidos..."
              className="pl-10 pr-4 py-2 w-full border rounded-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button className="border px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-gray-50">
            <Filter className="w-5 h-5" />
            <span>Filtros</span>
          </button>
        </div>

        {/* Orders Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pedido</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 cursor-pointer">
                  <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{order.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{order.customer}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500">{order.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-500">{order.items}</td>
                  <td className="px-6 py-4 whitespace-nowrap font-medium">{order.total}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(order.status)}
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Orders;
