import { industriesPPE } from '../config/ppeData';
import { supabase as supabaseClientUntyped } from '../lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabase: SupabaseClient<Database> = supabaseClientUntyped as SupabaseClient<Database>;

export const getAllPpeVisualizerImageUrls = (): string[] => {
  return industriesPPE.map(industry => {
    const { data } = supabase.storage
      .from('static-images') // Asegúrate que este sea el nombre correcto de tu bucket
      .getPublicUrl(`industry-selector/body-${industry.name.toLowerCase()}.jpg`);
    return data?.publicUrl || '/images/placeholder-product.jpg'; // Fallback por si alguna URL no se genera
  }).filter(url => url !== '/images/placeholder-product.jpg'); // Opcional: no precargar el placeholder
};

// Función para precargar una lista de imágenes
export const preloadImages = (urls: string[]): void => {
  console.log(`Preloading ${urls.length} images in background...`);
  urls.forEach(url => {
    const img = new Image();
    img.src = url;
    // Opcional: puedes añadir manejadores onload/onerror para logging detallado
    // img.onload = () => console.log(`Successfully preloaded: ${url}`);
    // img.onerror = () => console.warn(`Failed to preload: ${url}`);
  });
};