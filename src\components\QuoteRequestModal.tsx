import React, { useState } from 'react';
import { X } from 'lucide-react';
import { toast } from 'react-toastify';

interface QuoteRequestModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: QuoteRequestData) => void;
}

interface QuoteRequestData {
    fullName: string;
    company: string;
    email: string;
}

const QuoteRequestModal: React.FC<QuoteRequestModalProps> = ({ isOpen, onClose, onSubmit }) => {
    const [formData, setFormData] = useState<QuoteRequestData>({
        fullName: '',
        company: '',
        email: ''
    });

    // Función para validar el email
    const isValidEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    // Función para verificar si el formulario es válido
    const isFormValid = () => {
        return (
            formData.fullName.trim().length >= 3 && // Al menos 3 caracteres para el nombre
            formData.company.trim().length >= 2 && // Al menos 2 caracteres para la empresa
            isValidEmail(formData.email.trim()) // Email válido
        );
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (isFormValid()) {
            onSubmit(formData);
            setFormData({
                fullName: '',
                company: '',
                email: ''
            });
            
            // Mostrar mensaje de confirmación
            toast.success(
                <div className="text-center">
                    <p className="font-semibold mb-1">¡Gracias por su solicitud!</p>
                    <p>Nos contactaremos a la brevedad.</p>
                    <p className="text-sm mt-1">Agradecemos su confianza.</p>
                </div>,
                {
                    position: "top-center",
                    autoClose: 5000,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                }
            );

            // Cerrar el modal después de un breve delay
            setTimeout(() => {
                onClose();
            }, 1000);
        }
    };

    if (!isOpen) return null;

    return (
        <div 
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]"
            onClick={(e) => e.stopPropagation()}
        >
            <div 
                className="quote-modal-container bg-white rounded-lg p-6 w-full max-w-md"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">Solicitar Cotización</h2>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onClose();
                        }}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>
                
                <form 
                    onSubmit={handleSubmit} 
                    className="space-y-4"
                >
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Nombre completo
                        </label>
                        <input
                            type="text"
                            required
                            minLength={3}
                            value={formData.fullName}
                            onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                            className="w-full px-3 py-2 border rounded-lg focus:ring-1 focus:ring-blue-500"
                            placeholder="Ingrese al menos 3 caracteres"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Empresa
                        </label>
                        <input
                            type="text"
                            required
                            minLength={2}
                            value={formData.company}
                            onChange={(e) => setFormData({...formData, company: e.target.value})}
                            className="w-full px-3 py-2 border rounded-lg focus:ring-1 focus:ring-blue-500"
                            placeholder="Ingrese al menos 2 caracteres"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Correo electrónico
                        </label>
                        <input
                            type="email"
                            required
                            value={formData.email}
                            onChange={(e) => setFormData({...formData, email: e.target.value})}
                            className="w-full px-3 py-2 border rounded-lg focus:ring-1 focus:ring-blue-500"
                            placeholder="<EMAIL>"
                        />
                    </div>
                    
                    <button
                        type="submit"
                        disabled={!isFormValid()}
                        className={`w-full py-2 rounded-lg font-semibold transition duration-300
                            ${isFormValid() 
                                ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                                : 'bg-gray-300 cursor-not-allowed text-gray-500'}`}
                    >
                        Enviar Solicitud
                    </button>
                </form>
            </div>
        </div>
    );
};

export default QuoteRequestModal;




