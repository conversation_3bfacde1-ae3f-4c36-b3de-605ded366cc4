-- Modificar la tabla products para soportar arrays (si aún no son arrays)
DO $$
BEGIN
    -- Convertir categories a array si no lo es ya
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'categories' 
        AND data_type != 'ARRAY'
    ) THEN
        ALTER TABLE public.products 
            ALTER COLUMN categories SET DATA TYPE text[] USING ARRAY[categories];
    END IF;

    -- Convertir industries a array si no lo es ya
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'industries' 
        AND data_type != 'ARRAY'
    ) THEN
        ALTER TABLE public.products 
            ALTER COLUMN industries SET DATA TYPE text[] USING ARRAY[industries];
    END IF;
END $$;

-- Crear índices para búsqueda eficiente (si no existen)
DROP INDEX IF EXISTS idx_products_categories;
DROP INDEX IF EXISTS idx_products_industries;

CREATE INDEX idx_products_categories ON public.products USING GIN (categories);
CREATE INDEX idx_products_industries ON public.products USING GIN (industries);
