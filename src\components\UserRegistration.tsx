import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRegister } from '../hooks/useRegister';
import { toast } from 'react-toastify';

const UserRegistration = () => {
  const navigate = useNavigate();
  const { register, loading, error } = useRegister();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    client_type: 'small' as const,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await register(formData);
    
    if (result.success) {
      toast.success('Registro exitoso');
      navigate('/perfil');
    } else {
      toast.error(result.error || 'Error en el registro');
    }
  };

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Registro de Usuario</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Nombre</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            className="w-full p-2 border rounded"
            required
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Email</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            className="w-full p-2 border rounded"
            required
          />
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Contraseña</label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({...formData, password: e.target.value})}
            className="w-full p-2 border rounded"
            required
          />
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 mb-2">Tipo de Cliente</label>
          <select
            value={formData.client_type}
            onChange={(e) => setFormData({...formData, client_type: e.target.value as 'small' | 'medium' | 'large'})}
            className="w-full p-2 border rounded"
            required
          >
            <option value="small">Pequeña Empresa</option>
            <option value="medium">Mediana Empresa</option>
            <option value="large">Gran Empresa</option>
          </select>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded"
        >
          {loading ? 'Registrando...' : 'Registrarse'}
        </button>

        {error && (
          <p className="mt-4 text-red-500 text-sm">{error}</p>
        )}
      </form>
    </div>
  );
};

export default UserRegistration;
