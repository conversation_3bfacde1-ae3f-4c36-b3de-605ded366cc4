import { createClient } from '@supabase/supabase-js';
import winston from 'winston';
import { format } from 'winston';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

// Función para ocultar claves sensibles
const maskKey = (key) => {
  if (!key) return '✗';
  return key.length > 8 ? '✓' : '✗';
};

// Verificar variables de entorno de forma segura
if (!process.env.VITE_SUPABASE_URL || !process.env.VITE_SUPABASE_ANON_KEY) {
  console.error('Missing Supabase environment variables in logger');
  console.error('SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? '✓' : '✗');
  console.error('SUPABASE_KEY:', maskKey(process.env.VITE_SUPABASE_ANON_KEY));
}

// Inicializar cliente Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL || '',
  process.env.VITE_SUPABASE_ANON_KEY || ''
);

const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    // Asegurar que no se registren datos sensibles
    format.printf(info => {
      if (info.supabaseKey) info.supabaseKey = '[REDACTED]';
      return JSON.stringify(info);
    }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ]
});

export const requestLogger = async (req, res, next) => {
  const start = Date.now();
  res.on('finish', async () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      path: req.path,
      user_id: req.user?.id,
      status: res.statusCode,
      duration,
      ip_address: req.ip,
      user_agent: req.get('user-agent')
    };

    // Log to Winston
    logger.info('API Request', logData);

    // Log to Supabase only in production and if credentials are available
    if (process.env.NODE_ENV === 'production' && 
        process.env.VITE_SUPABASE_URL && 
        process.env.VITE_SUPABASE_ANON_KEY) {
      try {
        await supabase.from('api_logs').insert([logData]);
      } catch (error) {
        logger.error('Failed to log to Supabase', { 
          error: error.message // Solo registrar el mensaje de error, no el error completo
        });
      }
    }
  });
  next();
};

export default logger;



