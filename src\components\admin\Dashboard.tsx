import React from 'react';
import { Users, Package, ShoppingCart, TrendingUp } from 'lucide-react';
import DemoBanner from '../DemoBanner';

const Dashboard = () => {
  const stats = [
    { title: 'Usuarios Totales', value: '1,234', icon: Users, change: '+12%', color: 'bg-blue-500' },
    { title: 'Productos', value: '456', icon: Package, change: '+23%', color: 'bg-green-500' },
    { title: 'Pedidos', value: '89', icon: ShoppingCart, change: '+7%', color: 'bg-amber-500' },
    { title: 'Ventas', value: '$12,345', icon: TrendingUp, change: '+18%', color: 'bg-purple-500' },
  ];

  return (
    <div className="pt-16">
      <DemoBanner />
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Panel de Control</h1>
        
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <h3 className="text-2xl font-bold mt-1">{stat.value}</h3>
                </div>
                <div className={`${stat.color} p-3 rounded-full`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-green-600">{stat.change}</span>
                <span className="text-gray-600 ml-2">vs mes anterior</span>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Actividad Reciente</h2>
          <div className="space-y-4">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center space-x-4">
                  <div className="bg-gray-100 p-2 rounded-full">
                    <ShoppingCart className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium">Nuevo pedido #12345</p>
                    <p className="text-sm text-gray-600">Hace 2 horas</p>
                  </div>
                </div>
                <span className="text-amber-600 font-medium">$123.45</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
