-- Crear buckets para imágenes
begin;
  -- Bucket principal para imágenes de productos
  insert into storage.buckets (id, name, public)
  values (
    'product-images',
    'product-images',
    true
  ) on conflict do nothing;

  -- Bucket para imágenes temporales (ej: durante la carga)
  insert into storage.buckets (id, name, public)
  values (
    'temp-uploads',
    'temp-uploads',
    true
  ) on conflict do nothing;

  -- Políticas de acceso para product-images
  create policy "Imágenes públicamente accesibles"
  on storage.objects for select
  using ( bucket_id = 'product-images' );

  create policy "Solo usuarios autenticados pueden subir imágenes"
  on storage.objects for insert
  with check ( 
    bucket_id = 'product-images' 
    and auth.role() = 'authenticated'
  );

  create policy "Solo propietario o admin pueden actualizar"
  on storage.objects for update
  using ( 
    bucket_id = 'product-images' 
    and (auth.uid() = owner or auth.role() = 'admin')
  );

  create policy "Solo propietario o admin pueden eliminar"
  on storage.objects for delete
  using ( 
    bucket_id = 'product-images' 
    and (auth.uid() = owner or auth.role() = 'admin')
  );

  -- Políticas para temp-uploads
  create policy "Acceso temporal público"
  on storage.objects for select
  using ( bucket_id = 'temp-uploads' );

  create policy "Permitir carga temporal"
  on storage.objects for insert
  with check ( bucket_id = 'temp-uploads' );

  -- Eliminar archivos temporales después de 24 horas
  create extension if not exists pg_cron;

  select cron.schedule(
    'cleanup-temp-uploads',
    '0 */24 * * *',
    $$
    delete from storage.objects
    where bucket_id = 'temp-uploads'
    and created_at < now() - interval '24 hours'
    $$
  );
commit;