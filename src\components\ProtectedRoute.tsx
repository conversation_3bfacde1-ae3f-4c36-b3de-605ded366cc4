import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = () => {
  const { isAuthenticated, userProfile } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute - Auth state:', { isAuthenticated, userProfile, path: location.pathname });

  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Verificar rutas administrativas
  if (location.pathname.startsWith('/admin') && userProfile?.client_type !== 'admin') {
    console.log('Access denied to admin route:', { userProfile, path: location.pathname });
    return <Navigate to="/unauthorized" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
