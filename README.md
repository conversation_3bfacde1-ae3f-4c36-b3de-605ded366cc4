# CR Work - Seguridad Industrial

![CR Work Logo](public/favicon.jpg)

Plataforma web para la comercialización de equipos de protección personal y elementos de seguridad industrial.

## 🚀 URLs de Producción

- Frontend: [https://cr-seg-ind.pages.dev](https://cr-seg-ind.pages.dev)
- Backend API: [https://cr-work.vercel.app](https://cr-work.vercel.app)

## 💻 Tecnologías

- **Frontend**: React 18 + TypeScript + Vite 5
- **UI/Componentes**: 
  - Tailwind CSS 3
  - Lucide React (iconos)
  - React Query
  - Framer Motion
  - React Toastify
  - React Slick (carruseles)
- **Backend**: Express.js
- **Base de datos**: Supabase
- **Despliegue**: 
  - Frontend: Cloudflare Pages
  - Backend: Vercel / Railway

## 📋 Prerrequisitos

- Node.js >= 18.0.0
- npm >= 9.0.0
- Cuenta en Supabase
- Cuenta en Cloudflare (para despliegue frontend)
- Cuenta en Vercel o Railway (para despliegue backend)

## 🔧 Instalación

1. Clonar el repositorio
```bash
git clone https://github.com/tu-usuario/cr-seg-ind.git
cd cr-seg-ind
```

2. Instalar dependencias
```bash
npm install
```

3. Copiar el archivo de variables de entorno
```bash
cp .env.example .env
```

4. Configurar las variables de entorno en el archivo `.env`
```env
VITE_SUPABASE_URL=tu_url_de_supabase
VITE_SUPABASE_ANON_KEY=tu_key_de_supabase
VITE_API_URL=http://localhost:3010
```

## 🚀 Scripts Disponibles

- `npm run dev`: Inicia el servidor de desarrollo frontend (Vite)
- `npm run dev:server`: Inicia el servidor de desarrollo backend (Nodemon)
- `npm run build`: Construye la aplicación para producción
- `npm start`: Inicia el servidor de producción
- `npm run preview`: Vista previa de la build local
- `npm run verify-images`: Verifica la integridad de las imágenes
- `npm run migrate-images`: Migra imágenes al nuevo formato
- `npm run update-image-urls`: Actualiza URLs de imágenes en la base de datos
- `npm run version:major|minor|patch`: Actualiza la versión del proyecto

## 📁 Estructura del Proyecto

```
cr-seg-ind/
├── public/            # Archivos estáticos
├── src/
│   ├── components/    # Componentes React
│   ├── config/        # Configuraciones
│   ├── lib/           # Utilidades y helpers
│   └── types/         # Tipos TypeScript
├── scripts/           # Scripts de utilidad
├── supabase/          # Migraciones y configuración de Supabase
├── server.js          # Servidor Express
└── vite.config.ts     # Configuración de Vite
```

## 📦 Despliegue

### Cloudflare Pages (Frontend)

1. Conectar el repositorio a Cloudflare Pages
2. Configurar build settings:
   - Framework preset: Vite
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Node version: 18
3. Configurar variables de entorno en Cloudflare Pages

### Vercel (Backend)

1. Conectar el repositorio a Vercel
2. Configurar variables de entorno en Vercel, incluyendo:
   - `PORT=3010`
   - `NODE_ENV=production`
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
   - `CORS_ORIGINS` (orígenes permitidos para CORS, separados por comas)
3. El despliegue se realizará automáticamente

### Railway (Alternativa para Backend)

1. Conectar el repositorio a Railway
2. Railway detectará automáticamente la configuración en `railway.toml`
3. Configurar las variables de entorno necesarias
4. El despliegue se realizará automáticamente

## 🔑 Variables de Entorno

### Frontend (Cloudflare Pages)
```env
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
VITE_API_URL=
```

### Backend (Vercel/Railway)
```env
PORT=3010
NODE_ENV=production
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
CORS_ORIGINS=
```

## 🛠️ Mantenimiento

### Actualización de Productos

El proyecto incluye varios scripts para gestionar productos:

- `scripts/updateProductCategories.js`: Actualiza categorías de productos
- `scripts/updateImageUrls.js`: Sincroniza URLs de imágenes con Supabase Storage
- `scripts/updateFeaturedProducts.js`: Gestiona productos destacados

### Backups y Mantenimiento

Supabase está configurado con:
- Backups diarios automáticos (3 AM UTC)
- Sincronización de URLs de imágenes
- Verificación de estado del sistema

## 🤝 Contribuir

1. Fork el proyecto
2. Crea tu rama de feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE.md](LICENSE.md) para detalles

## 📞 Contacto

CR Work - [https://cr-seg-ind.pages.dev](https://cr-seg-ind.pages.dev)
