import React from 'react';
import { X } from 'lucide-react';

interface MapPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const MapPopup: React.FC<MapPopupProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl w-full max-w-3xl relative">
        <button
          onClick={onClose}
          className="absolute right-2 top-2 p-2 text-gray-500 hover:text-gray-700 z-10"
        >
          <X className="w-6 h-6" />
        </button>
        <div className="h-[500px] w-full rounded-xl overflow-hidden">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3278.199684432781!2d-58.79497262341577!3d-34.66270527298277!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x95bcc62b29c32a1b%3A0x9f1f8c3e6e0b7b9d!2sMerlo%202338%2C%20B1744OEJ%20Moreno%2C%20Provincia%20de%20Buenos%20Aires!5e0!3m2!1ses-419!2sar!4v1711039168421!5m2!1ses-419!2sar"
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen={true}
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
      </div>
    </div>
  );
};

export default MapPopup;