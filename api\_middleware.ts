import { VercelRequest, VercelResponse } from '@vercel/node';
import NodeCache from 'node-cache';

// Caché en memoria con TTL por ruta
const apiCache = new NodeCache({
  stdTTL: 60, // 60 segundos por defecto
  checkperiod: 120 // Verificar caducidad cada 2 minutos
});

// Configuración de TTL por ruta (en segundos)
const cacheTTLConfig: Record<string, number> = {
  '/api/health': 60,
  '/api/products': 300, // 5 minutos
  '/api/catalog': 300,
  '/api/featured-products': 300,
  // Añade más rutas según sea necesario
};

// Rutas que no deben cachearse
const noCacheRoutes = [
  '/api/auth',
  '/api/checkout'
  // Añade más rutas según sea necesario
];

export default function cacheMiddleware(handler: any) {
  return async (req: VercelRequest, res: VercelResponse) => {
    // No cachear métodos que no sean GET
    if (req.method !== 'GET') {
      return handler(req, res);
    }

    const url = req.url || '';
    
    // Verificar si la ruta está en la lista de no-caché
    if (noCacheRoutes.some(route => url.startsWith(route))) {
      return handler(req, res);
    }
    
    // Crear una clave de caché basada en la URL y los query params
    const cacheKey = `${url}${JSON.stringify(req.query)}`;
    
    // Verificar si tenemos una respuesta en caché
    const cachedResponse = apiCache.get(cacheKey);
    if (cachedResponse) {
      res.setHeader('X-Cache', 'HIT');
      return res.status(200).json(cachedResponse);
    }
    
    // Modificar res.json para interceptar y cachear la respuesta
    const originalJson = res.json;
    res.json = function(body) {
      // Solo cachear respuestas exitosas
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Obtener TTL para esta ruta
        const ttl = Object.entries(cacheTTLConfig).find(
          ([route]) => url.startsWith(route)
        )?.[1] || 60; // TTL por defecto
        
        // Guardar en caché
        apiCache.set(cacheKey, body, ttl);
        res.setHeader('X-Cache', 'MISS');
        res.setHeader('Cache-Control', `public, max-age=${ttl}`);
      }
      
      return originalJson.call(this, body);
    };
    
    // Continuar con el handler original
    return handler(req, res);
  };
}