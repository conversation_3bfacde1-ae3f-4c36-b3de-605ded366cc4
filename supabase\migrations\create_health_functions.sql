create or replace function public.update_health_check()
returns void as $$
begin
    insert into public.health_check (status, details)
    values (
        'ok',
        jsonb_build_object(
            'timestamp', now(),
            'database_size', pg_size_pretty(pg_database_size(current_database())),
            'connection_count', (select count(*) from pg_stat_activity)
        )
    );

    -- Mantener solo los últimos 100 registros
    delete from public.health_check
    where id not in (
        select id from public.health_check
        order by last_check desc
        limit 100
    );
end;
$$ language plpgsql security definer;

-- Programar health check cada 5 minutos
select cron.schedule(
    'health-check-update',
    '*/5 * * * *',
    $$select public.update_health_check()$$
);