interface CategoryCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  variant?: 'primary' | 'secondary' | 'featured';
  imageUrl?: string; // Nueva propiedad para la imagen
  onClick?: () => void;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  title,
  description,
  icon: Icon,
  variant = 'primary',
  imageUrl,
  onClick
}) => {
  return (
    <div 
      className={`category-card ${variant === 'featured' ? 'category-card--featured' : ''}`}
      onClick={onClick}
    >
      {imageUrl && (
        <div className="card-image-container">
          <img 
            src={imageUrl} 
            alt={title} 
            className="card-image" 
          />
        </div>
      )}
      <div className="category-icon-wrapper">
        <Icon className="category-icon" />
      </div>
      <h3 className="category-title">{title}</h3>
      <p className="category-description">{description}</p>
    </div>
  );
};

export const ServiceCard: React.FC<CategoryCardProps> = ({
  title,
  description,
  icon: Icon,
  variant = 'primary',
  imageUrl,
  onClick
}) => {
  return (
    <div 
      className={`service-card service-card--${variant}`}
      onClick={onClick}
    >
      {imageUrl && (
        <div className="card-image-container">
          <img 
            src={imageUrl} 
            alt={title} 
            className="card-image" 
          />
        </div>
      )}
      <div className="service-icon-wrapper">
        <Icon className="service-icon" />
      </div>
      <h3 className="service-title">{title}</h3>
      <p className="service-description">{description}</p>
    </div>
  );
};