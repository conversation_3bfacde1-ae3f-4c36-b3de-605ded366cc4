﻿Special instructions for CLINE        


1. Add supabase for the form
2. Add pictures of real products for the product cards
3. Add a favicon
4. Add the all the main product categories of the industry
5. Colors keep the same
6. 

You are inside a starting project with nodeJS & express, with its FE in React & tailwind. I am on windows powershell so don't use && symbols
You are already inside a fresh nextjs project and there are images inside a folder called /Public/images/, it is a service based website, for a company that offer these products  <products>, the website should be in spanish and the website should be properly split with href language tags before the main slug of the url, for example example.com/fr/example/example. 
Use the images in an intelligent way to build a modern website with good coloring schemes and fonts and other elements which I will leave up to your discretion to plan and then implement a good, intelligent color and font and feel to it
Use <service_information> to understand specifics of the business
For languages, ensure to implement the SEO and keywords etc for both spanish and english - Also URL slugs must be translated 
The company is offering products for industries in <industries>
The idea is to generate all possible pages, combining both <services> with <locations> to create location based SEO services.
Ensure the pages are split by industry, so no two industries landing page should look the same (even if the industry page do look the same)
Make sure the colors are contrasting and not white on white or black on black at any point
The content of those pages should be landing pages for the service itself, created from a template that you have built, using the images, and the other information you know or can interpret from what you’ve been given.
Have a good looking contact us page with the <contact_details> on it
Ensure to create a phenomenal modern homepage for the website using the images and information about services to make a modular, mobilefriendly (it must not scroll horizontally on mobile) homepage


<products>
Elementos de seguridad industrial
Protección física
Protección química
Elementos visuales dissuasórios
</products>

<industries>
AERONAUTICA
AGRICOLA
ALIMENTICIA
AUTOMOTRIZ
AVICOLA
CARPINTERIA
CEMENTERA
CONSTRUCCION
VIALIDAD
ELECTRICA
ELECTRONICA
FARMACEUTICA
FERRETERIA
FRIGORIFICA
GAS
LABORATORIO
LIMPIEZA
LOGISTICA
MANUFACTURA
METALMECANICA
METALURGICA
MINERA
NAVIERA
PESQUERA
PETROLEO
PETROLERA
PETROQUIMICA
QUIMICA
SALUD
SIDERURGICA
SOLDADURA
TABACALERA
TRANSPORTE
VIALIDAD
</industries>


<contact_details>
+54 1100000000
</contact_details>


Create icons and svgs as you’re going - start with something simple
Implement ISR so the website can be built again quickly and easily
Use NextJS, and tailwind to make a unique beautiful modern modular website with 5-7 unique vertical blocks per page (more on the homepage)
Be very careful and wary of typescript errors
Make sure you are generatingStaticParams - and not confusing dynamic generation and static generation, 
Maximise build efficiency, speed, and complexity and modular nature of any pages which are generated for SEO.
Ensure to implement all slugs etc programmatically, and never create an index page link without creating the index page itself

You must be as detailed as possible with your SEO, abusing the fact that Google is very likely to rank pages that have exact phrase matches to keywords, for example “What is the meaning and Origin of X name” for my baby name website, which helps me rank for that question across all of the names in my database (which is 88k names) with a total of 100k pages, you can see how index pages, and then individual pages of that page type can really start to create scale.